# 环境配置说明

## 概述

E-Invoice服务支持三个环境：
- **default**: 默认开发环境
- **beta**: 正式测试环境  
- **prod**: 生产环境

每个环境都有独立的配置文件，通过 `spring.profiles.active` 参数指定。

## 配置文件结构

```
src/main/resources/
├── application.properties              # 主配置文件 (公共配置)
├── application-default.properties      # 默认环境配置
├── application-beta.properties         # Beta环境配置
└── application-prod.properties         # 生产环境配置
```

## 环境详细配置

### Default 环境 (开发环境)

**配置文件**: `application-default.properties`

**特点**:
- 使用 H2 内存数据库
- 启用 H2 控制台
- 使用 MyInvois 沙盒环境
- 本地 Redis (可选)
- 详细的调试日志

**启动方式**:
```bash
# 方式1: 使用脚本
./scripts/start-default.sh

# 方式2: 直接启动
java -Dspring.profiles.active=default -jar e-invoice-service/target/e-invoice.jar

# 方式3: 环境变量
export SPRING_PROFILES_ACTIVE=default
java -jar e-invoice-service/target/e-invoice.jar
```

**必需环境变量**:
```bash
# MyInvois 沙盒配置 (可选，有默认值)
export MYINVOIS_SANDBOX_CLIENT_ID=your-sandbox-client-id
export MYINVOIS_SANDBOX_CLIENT_SECRET=your-sandbox-client-secret

# Redis 配置 (可选，默认本地)
export REDIS_HOST=localhost
export REDIS_PORT=6379
export REDIS_DATABASE=0
export REDIS_PASSWORD=
```

**访问地址**:
- 应用: http://localhost:8080
- H2控制台: http://localhost:8080/h2-console
- 健康检查: http://localhost:8080/api/v1/invoices/health

### Beta 环境 (正式测试环境)

**配置文件**: `application-beta.properties`

**特点**:
- 使用 MySQL 数据库
- 使用 MyInvois 沙盒环境 (用于测试)
- 阿里云 Redis (数据库111)
- 适中的日志级别

**启动方式**:
```bash
# 使用脚本
./scripts/start-beta.sh

# 直接启动
java -Dspring.profiles.active=beta -jar e-invoice-service/target/e-invoice.jar
```

**必需环境变量**:
```bash
# 数据库配置
export DB_HOST=your-mysql-host
export DB_PORT=3306
export DB_NAME=einvoice_beta
export DB_USERNAME=your-username
export DB_PASSWORD=your-password

# MyInvois Beta配置
export MYINVOIS_BETA_CLIENT_ID=your-beta-client-id
export MYINVOIS_BETA_CLIENT_SECRET=your-beta-client-secret

# Redis配置 (可选，有默认值)
export REDIS_HOST=r-8vbkddg0ez3eak2rzq.redis.zhangbei.rds.aliyuncs.com
export REDIS_PORT=6379
export REDIS_DATABASE=111
export REDIS_PASSWORD=roFXzHwXPY3RnI%5
```

### Prod 环境 (生产环境)

**配置文件**: `application-prod.properties`

**特点**:
- 使用 MySQL 数据库 (生产级配置)
- 使用 MyInvois 正式环境
- 阿里云 Redis (数据库110)
- 最小化日志输出
- 优化的JVM参数

**启动方式**:
```bash
# 使用脚本 (推荐)
./scripts/start-prod.sh

# 直接启动
java -Dspring.profiles.active=prod -jar e-invoice-service/target/e-invoice.jar
```

**必需环境变量**:
```bash
# 数据库配置 (必需)
export DB_HOST=your-production-mysql-host
export DB_PORT=3306
export DB_NAME=einvoice
export DB_USERNAME=your-production-username
export DB_PASSWORD=your-production-password

# MyInvois 生产配置 (必需)
export MYINVOIS_PROD_CLIENT_ID=your-production-client-id
export MYINVOIS_PROD_CLIENT_SECRET=your-production-client-secret

# Redis配置 (可选，有默认值)
export REDIS_HOST=r-8vbkddg0ez3eak2rzq.redis.zhangbei.rds.aliyuncs.com
export REDIS_PORT=6379
export REDIS_DATABASE=110
export REDIS_PASSWORD=roFXzHwXPY3RnI%5
```

## 环境切换

### 1. 通过环境变量
```bash
export SPRING_PROFILES_ACTIVE=beta
java -jar e-invoice-service/target/e-invoice.jar
```

### 2. 通过JVM参数
```bash
java -Dspring.profiles.active=prod -jar e-invoice-service/target/e-invoice.jar
```

### 3. 通过配置文件
在 `application.properties` 中修改:
```properties
spring.profiles.active=beta
```

## 环境验证

### 检查当前环境
```bash
curl http://localhost:8080/api/v1/env/info
```

### 检查环境健康状态
```bash
curl http://localhost:8080/api/v1/env/health
```

### 检查Redis连接
```bash
curl http://localhost:8080/api/v1/cache/health
```

## 配置优先级

Spring Boot 配置加载优先级 (从高到低):
1. 命令行参数
2. 环境变量
3. `application-{profile}.properties`
4. `application.properties`

## 最佳实践

### 开发环境
- 使用 default 环境
- 本地启动 Redis (可选)
- 使用 H2 控制台查看数据

### 测试环境
- 使用 beta 环境
- 连接测试数据库
- 使用 MyInvois 沙盒环境

### 生产环境
- 使用 prod 环境
- 所有敏感信息通过环境变量传入
- 定期备份数据库
- 监控应用性能和日志

## 故障排除

### 1. 配置文件未找到
确保配置文件名称正确：`application-{profile}.properties`

### 2. 环境变量未生效
检查环境变量是否正确设置：
```bash
echo $SPRING_PROFILES_ACTIVE
```

### 3. 数据库连接失败
检查数据库配置和网络连接

### 4. Redis连接失败
使用缓存健康检查接口验证连接

### 5. MyInvois认证失败
检查客户端ID和密钥是否正确配置
