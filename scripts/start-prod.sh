#!/bin/bash

# 启动生产环境
echo "=================================================================="
echo "启动 E-Invoice 服务 - Prod 环境 (生产环境)"
echo "=================================================================="

# 设置环境变量
export SPRING_PROFILES_ACTIVE=prod

# 数据库配置 (生产环境必需)
export DB_HOST=${DB_HOST}
export DB_PORT=${DB_PORT:-"3306"}
export DB_NAME=${DB_NAME:-"einvoice"}
export DB_USERNAME=${DB_USERNAME}
export DB_PASSWORD=${DB_PASSWORD}

# MyInvois 生产环境配置
export MYINVOIS_PROD_CLIENT_ID=${MYINVOIS_PROD_CLIENT_ID}
export MYINVOIS_PROD_CLIENT_SECRET=${MYINVOIS_PROD_CLIENT_SECRET}

# Redis配置 (阿里云Redis - 生产数据库)
export REDIS_HOST=${REDIS_HOST:-"r-8vbkddg0ez3eak2rzq.redis.zhangbei.rds.aliyuncs.com"}
export REDIS_PORT=${REDIS_PORT:-"6379"}
export REDIS_DATABASE=${REDIS_DATABASE:-"110"}
export REDIS_PASSWORD=${REDIS_PASSWORD:-"roFXzHwXPY3RnI%5"}

echo "环境配置:"
echo "  Profile: $SPRING_PROFILES_ACTIVE"
echo "  配置文件: application-prod.properties"
echo "  MyInvois: 正式环境"
echo "  Database: MySQL ($DB_HOST:$DB_PORT/$DB_NAME)"
echo "  Redis: $REDIS_HOST:$REDIS_PORT (DB: $REDIS_DATABASE)"
echo "=================================================================="

# 检查必需的环境变量
missing_vars=()

if [ -z "$DB_HOST" ]; then
    missing_vars+=("DB_HOST")
fi

if [ -z "$DB_USERNAME" ]; then
    missing_vars+=("DB_USERNAME")
fi

if [ -z "$DB_PASSWORD" ]; then
    missing_vars+=("DB_PASSWORD")
fi

if [ -z "$MYINVOIS_PROD_CLIENT_ID" ]; then
    missing_vars+=("MYINVOIS_PROD_CLIENT_ID")
fi

if [ -z "$MYINVOIS_PROD_CLIENT_SECRET" ]; then
    missing_vars+=("MYINVOIS_PROD_CLIENT_SECRET")
fi

if [ ${#missing_vars[@]} -ne 0 ]; then
    echo "错误: 以下必需的环境变量未设置:"
    for var in "${missing_vars[@]}"; do
        echo "  - $var"
    done
    echo ""
    echo "请设置这些环境变量后重新运行脚本"
    exit 1
fi

echo "环境变量检查通过，启动应用..."

# 启动应用 (生产环境使用更多的JVM参数)
java -Xms512m -Xmx2g -XX:+UseG1GC -XX:+UseStringDeduplication \
     -Dspring.profiles.active=prod \
     -jar ../e-invoice-service/target/e-invoice.jar
