#!/bin/bash

# 启动默认环境 (开发环境)
echo "启动 E-Invoice 服务 - Default 环境 (开发环境)"

# 设置环境变量
export SPRING_PROFILES_ACTIVE=default

# MyInvois 沙盒环境配置
export MYINVOIS_SANDBOX_CLIENT_ID=${MYINVOIS_SANDBOX_CLIENT_ID:-"your-sandbox-client-id"}
export MYINVOIS_SANDBOX_CLIENT_SECRET=${MYINVOIS_SANDBOX_CLIENT_SECRET:-"your-sandbox-client-secret"}

# Redis 配置 (本地开发)
export REDIS_HOST=${REDIS_HOST:-"localhost"}
export REDIS_PORT=${REDIS_PORT:-"6379"}
export REDIS_DATABASE=${REDIS_DATABASE:-"0"}
export REDIS_PASSWORD=${REDIS_PASSWORD:-""}

echo "环境配置:"
echo "  Profile: $SPRING_PROFILES_ACTIVE"
echo "  MyInvois: 沙盒环境"
echo "  Database: H2 内存数据库"
echo "  Redis: $REDIS_HOST:$REDIS_PORT (DB: $REDIS_DATABASE)"

# 启动应用
java -jar ../e-invoice-service/target/e-invoice.jar
