#!/bin/bash

# 启动Beta环境 (正式测试环境)
echo "=================================================================="
echo "启动 E-Invoice 服务 - Beta 环境 (正式测试环境)"
echo "=================================================================="

# 设置环境变量
export SPRING_PROFILES_ACTIVE=beta

# 数据库配置
export DB_HOST=${DB_HOST:-"localhost"}
export DB_PORT=${DB_PORT:-"3306"}
export DB_NAME=${DB_NAME:-"einvoice_beta"}
export DB_USERNAME=${DB_USERNAME:-"einvoice"}
export DB_PASSWORD=${DB_PASSWORD}

# MyInvois Beta环境配置 (仍使用沙盒)
export MYINVOIS_BETA_CLIENT_ID=${MYINVOIS_BETA_CLIENT_ID}
export MYINVOIS_BETA_CLIENT_SECRET=${MYINVOIS_BETA_CLIENT_SECRET}

# Redis配置 (阿里云Redis - Beta数据库)
export REDIS_HOST=${REDIS_HOST:-"r-8vbkddg0ez3eak2rzq.redis.zhangbei.rds.aliyuncs.com"}
export REDIS_PORT=${REDIS_PORT:-"6379"}
export REDIS_DATABASE=${REDIS_DATABASE:-"111"}
export REDIS_PASSWORD=${REDIS_PASSWORD:-"roFXzHwXPY3RnI%5"}

echo "环境配置:"
echo "  Profile: $SPRING_PROFILES_ACTIVE"
echo "  配置文件: application-beta.properties"
echo "  MyInvois: 沙盒环境 (测试)"
echo "  Database: MySQL ($DB_HOST:$DB_PORT/$DB_NAME)"
echo "  Redis: $REDIS_HOST:$REDIS_PORT (DB: $REDIS_DATABASE)"
echo "=================================================================="

# 检查必需的环境变量
if [ -z "$DB_PASSWORD" ]; then
    echo "错误: DB_PASSWORD 环境变量未设置"
    exit 1
fi

if [ -z "$MYINVOIS_BETA_CLIENT_ID" ]; then
    echo "错误: MYINVOIS_BETA_CLIENT_ID 环境变量未设置"
    exit 1
fi

if [ -z "$MYINVOIS_BETA_CLIENT_SECRET" ]; then
    echo "错误: MYINVOIS_BETA_CLIENT_SECRET 环境变量未设置"
    exit 1
fi

echo "环境变量检查通过，启动应用..."

# 启动应用
java -jar ../e-invoice-service/target/e-invoice.jar
