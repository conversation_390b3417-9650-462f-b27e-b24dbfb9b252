# 马来西亚电子发票服务 - 快速启动指南

## 🚀 快速开始

### 1. 环境准备

确保你的开发环境满足以下要求：
- Java 1.8+
- Maven 3.6+
- Git

### 2. 获取MyInvois API凭证

1. 访问 [MyInvois开发者门户](https://myinvois.hasil.gov.my/)
2. 注册开发者账户
3. 创建应用程序并获取：
   - Client ID
   - Client Secret

### 3. 配置项目

#### 方式一：修改配置文件

编辑 `e-invoice-service/src/main/resources/application.yml`：

```yaml
myinvois:
  auth:
    client-id: 你的客户端ID
    client-secret: 你的客户端密钥
```

#### 方式二：使用环境变量

```bash
export MYINVOIS_SANDBOX_CLIENT_ID=你的沙盒客户端ID
export MYINVOIS_SANDBOX_CLIENT_SECRET=你的沙盒客户端密钥
```

### 4. 构建和运行

```bash
# 克隆项目
git clone <your-repo-url>
cd e-invoice

# 构建项目
mvn clean package -DskipTests

# 运行应用
java -jar e-invoice-service/target/e-invoice.jar
```

### 5. 验证安装

访问以下URL验证服务是否正常运行：

- 应用健康检查: http://localhost:8080/api/v1/invoices/health
- MyInvois健康检查: http://localhost:8080/api/v1/myinvois/health
- H2数据库控制台: http://localhost:8080/h2-console

## 📝 创建第一张发票

### 1. 准备测试数据

创建文件 `test_invoice.json`：

```json
{
  "buyer_name": "测试买方公司",
  "buyer_tax_number": "123456789012345",
  "buyer_address": "测试买方地址",
  "buyer_phone": "+60123456789",
  "seller_name": "测试卖方公司",
  "seller_tax_number": "987654321098765",
  "seller_address": "测试卖方地址",
  "seller_phone": "+60987654321",
  "invoice_items": [
    {
      "item_name": "测试商品",
      "specification": "规格型号",
      "unit": "个",
      "quantity": 2,
      "unit_price": 100.00,
      "amount": 200.00,
      "tax_rate": 0.06,
      "tax_amount": 12.00
    }
  ],
  "remarks": "测试发票"
}
```

### 2. 创建发票

```bash
curl -X POST http://localhost:8080/api/v1/invoices \
  -H "Content-Type: application/json" \
  -d @test_invoice.json
```

### 3. 查看结果

成功响应示例：

```json
{
  "code": 200,
  "message": "发票创建成功",
  "data": {
    "invoice_id": "uuid-here",
    "invoice_number": "INV1234567890",
    "status": "SUBMITTED",
    "total_amount": 200.00,
    "tax_amount": 12.00,
    "amount_including_tax": 212.00
  },
  "timestamp": 1234567890
}
```

## 🔧 常见问题

### Q: 应用启动失败，提示OAuth2认证错误
A: 检查MyInvois API凭证是否正确配置，确保在沙盒环境中测试。

### Q: 发票提交到MyInvois失败
A: 
1. 检查网络连接
2. 验证API凭证是否有效
3. 确保发票数据符合UBL 2.1标准

### Q: 如何查看详细的错误日志
A: 检查应用日志，或者设置日志级别为DEBUG：

```yaml
logging:
  level:
    com.wosai.upay.einvoice: DEBUG
```

## 📚 下一步

1. 阅读完整的 [项目文档](PROJECT_SUMMARY.md)
2. 查看 [MyInvois官方文档](https://sdk.myinvois.hasil.gov.my/)
3. 在沙盒环境中充分测试
4. 配置生产环境凭证
5. 实施数字签名（生产环境必需）

## 🆘 获取帮助

如果遇到问题：

1. 检查 [常见问题解答](PROJECT_SUMMARY.md#重要注意事项)
2. 查看应用日志
3. 参考 [MyInvois官方文档](https://sdk.myinvois.hasil.gov.my/)
4. 联系技术支持

---

**注意**: 这是一个集成MyInvois API的完整解决方案，请确保在生产环境使用前充分测试所有功能。
