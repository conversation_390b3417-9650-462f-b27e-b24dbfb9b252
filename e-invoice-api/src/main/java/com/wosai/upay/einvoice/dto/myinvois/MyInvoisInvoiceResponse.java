package com.wosai.upay.einvoice.dto.myinvois;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;
import java.util.List;

/**
 * MyInvois发票提交响应DTO
 */
public class MyInvoisInvoiceResponse {
    
    @JsonProperty("submissionUid")
    private String submissionUid;
    
    @JsonProperty("acceptedDocuments")
    private List<AcceptedDocument> acceptedDocuments;
    
    @JsonProperty("rejectedDocuments")
    private List<RejectedDocument> rejectedDocuments;
    
    // Getters and Setters
    public String getSubmissionUid() {
        return submissionUid;
    }
    
    public void setSubmissionUid(String submissionUid) {
        this.submissionUid = submissionUid;
    }
    
    public List<AcceptedDocument> getAcceptedDocuments() {
        return acceptedDocuments;
    }
    
    public void setAcceptedDocuments(List<AcceptedDocument> acceptedDocuments) {
        this.acceptedDocuments = acceptedDocuments;
    }
    
    public List<RejectedDocument> getRejectedDocuments() {
        return rejectedDocuments;
    }
    
    public void setRejectedDocuments(List<RejectedDocument> rejectedDocuments) {
        this.rejectedDocuments = rejectedDocuments;
    }
    
    /**
     * 已接受的文档
     */
    public static class AcceptedDocument {
        
        @JsonProperty("uuid")
        private String uuid;
        
        @JsonProperty("invoiceCodeNumber")
        private String invoiceCodeNumber;
        
        // Getters and Setters
        public String getUuid() {
            return uuid;
        }
        
        public void setUuid(String uuid) {
            this.uuid = uuid;
        }
        
        public String getInvoiceCodeNumber() {
            return invoiceCodeNumber;
        }
        
        public void setInvoiceCodeNumber(String invoiceCodeNumber) {
            this.invoiceCodeNumber = invoiceCodeNumber;
        }
    }
    
    /**
     * 被拒绝的文档
     */
    public static class RejectedDocument {
        
        @JsonProperty("invoiceCodeNumber")
        private String invoiceCodeNumber;
        
        @JsonProperty("error")
        private ErrorDetail error;
        
        // Getters and Setters
        public String getInvoiceCodeNumber() {
            return invoiceCodeNumber;
        }
        
        public void setInvoiceCodeNumber(String invoiceCodeNumber) {
            this.invoiceCodeNumber = invoiceCodeNumber;
        }
        
        public ErrorDetail getError() {
            return error;
        }
        
        public void setError(ErrorDetail error) {
            this.error = error;
        }
    }
    
    /**
     * 错误详情
     */
    public static class ErrorDetail {
        
        @JsonProperty("code")
        private String code;
        
        @JsonProperty("message")
        private String message;
        
        @JsonProperty("target")
        private String target;
        
        @JsonProperty("details")
        private List<ErrorDetail> details;
        
        // Getters and Setters
        public String getCode() {
            return code;
        }
        
        public void setCode(String code) {
            this.code = code;
        }
        
        public String getMessage() {
            return message;
        }
        
        public void setMessage(String message) {
            this.message = message;
        }
        
        public String getTarget() {
            return target;
        }
        
        public void setTarget(String target) {
            this.target = target;
        }
        
        public List<ErrorDetail> getDetails() {
            return details;
        }
        
        public void setDetails(List<ErrorDetail> details) {
            this.details = details;
        }
    }
}
