package com.wosai.upay.einvoice.dto.ubl;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonRootName;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * UBL 2.1标准发票结构
 * 基于马来西亚MyInvois系统规范
 */
@JsonRootName("Invoice")
public class UBLInvoice {

    @NotBlank(message = "发票代码不能为空")
    @JsonProperty("ID")
    private String id; // e-Invoice Code / Number

    @NotNull(message = "发票日期不能为空")
    @JsonProperty("IssueDate")
    private LocalDate issueDate;

    @NotNull(message = "发票时间不能为空")
    @JsonProperty("IssueTime")
    private LocalTime issueTime;

    @NotNull(message = "发票类型代码不能为空")
    @JsonProperty("InvoiceTypeCode")
    private InvoiceTypeCode invoiceTypeCode;

    @NotBlank(message = "文档货币代码不能为空")
    @JsonProperty("DocumentCurrencyCode")
    private String documentCurrencyCode; // 默认MYR

    @JsonProperty("TaxCurrencyCode")
    private String taxCurrencyCode; // 可选，默认MYR

    @Valid
    @NotNull(message = "供应商信息不能为空")
    @JsonProperty("AccountingSupplierParty")
    private Party accountingSupplierParty;

    @Valid
    @NotNull(message = "买方信息不能为空")
    @JsonProperty("AccountingCustomerParty")
    private Party accountingCustomerParty;

    @JsonProperty("InvoicePeriod")
    private Object invoicePeriod; // 简化处理

    @Valid
    @NotNull(message = "发票行项目不能为空")
    @JsonProperty("InvoiceLine")
    private List<Object> invoiceLines; // 简化处理

    @Valid
    @NotNull(message = "税务总计不能为空")
    @JsonProperty("TaxTotal")
    private List<Object> taxTotals; // 简化处理

    @Valid
    @NotNull(message = "法定货币总计不能为空")
    @JsonProperty("LegalMonetaryTotal")
    private Object legalMonetaryTotal; // 简化处理

    @JsonProperty("PaymentMeans")
    private Object paymentMeans; // 简化处理

    @JsonProperty("PaymentTerms")
    private Object paymentTerms; // 简化处理

    @JsonProperty("PrepaidPayment")
    private Object prepaidPayment; // 简化处理

    @JsonProperty("AllowanceCharge")
    private List<Object> allowanceCharges; // 简化处理

    @JsonProperty("Delivery")
    private Object delivery; // 简化处理

    @JsonProperty("AdditionalDocumentReference")
    private List<Object> additionalDocumentReferences; // 简化处理

    @JsonProperty("Signature")
    private Object signature; // 简化处理

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public LocalDate getIssueDate() {
        return issueDate;
    }

    public void setIssueDate(LocalDate issueDate) {
        this.issueDate = issueDate;
    }

    public LocalTime getIssueTime() {
        return issueTime;
    }

    public void setIssueTime(LocalTime issueTime) {
        this.issueTime = issueTime;
    }

    public InvoiceTypeCode getInvoiceTypeCode() {
        return invoiceTypeCode;
    }

    public void setInvoiceTypeCode(InvoiceTypeCode invoiceTypeCode) {
        this.invoiceTypeCode = invoiceTypeCode;
    }

    public String getDocumentCurrencyCode() {
        return documentCurrencyCode;
    }

    public void setDocumentCurrencyCode(String documentCurrencyCode) {
        this.documentCurrencyCode = documentCurrencyCode;
    }

    public String getTaxCurrencyCode() {
        return taxCurrencyCode;
    }

    public void setTaxCurrencyCode(String taxCurrencyCode) {
        this.taxCurrencyCode = taxCurrencyCode;
    }

    public Party getAccountingSupplierParty() {
        return accountingSupplierParty;
    }

    public void setAccountingSupplierParty(Party accountingSupplierParty) {
        this.accountingSupplierParty = accountingSupplierParty;
    }

    public Party getAccountingCustomerParty() {
        return accountingCustomerParty;
    }

    public void setAccountingCustomerParty(Party accountingCustomerParty) {
        this.accountingCustomerParty = accountingCustomerParty;
    }

    public Object getInvoicePeriod() {
        return invoicePeriod;
    }

    public void setInvoicePeriod(Object invoicePeriod) {
        this.invoicePeriod = invoicePeriod;
    }

    public List<Object> getInvoiceLines() {
        return invoiceLines;
    }

    public void setInvoiceLines(List<Object> invoiceLines) {
        this.invoiceLines = invoiceLines;
    }

    public List<Object> getTaxTotals() {
        return taxTotals;
    }

    public void setTaxTotals(List<Object> taxTotals) {
        this.taxTotals = taxTotals;
    }

    public Object getLegalMonetaryTotal() {
        return legalMonetaryTotal;
    }

    public void setLegalMonetaryTotal(Object legalMonetaryTotal) {
        this.legalMonetaryTotal = legalMonetaryTotal;
    }

    public Object getPaymentMeans() {
        return paymentMeans;
    }

    public void setPaymentMeans(Object paymentMeans) {
        this.paymentMeans = paymentMeans;
    }

    public Object getPaymentTerms() {
        return paymentTerms;
    }

    public void setPaymentTerms(Object paymentTerms) {
        this.paymentTerms = paymentTerms;
    }

    public Object getPrepaidPayment() {
        return prepaidPayment;
    }

    public void setPrepaidPayment(Object prepaidPayment) {
        this.prepaidPayment = prepaidPayment;
    }

    public List<Object> getAllowanceCharges() {
        return allowanceCharges;
    }

    public void setAllowanceCharges(List<Object> allowanceCharges) {
        this.allowanceCharges = allowanceCharges;
    }

    public Object getDelivery() {
        return delivery;
    }

    public void setDelivery(Object delivery) {
        this.delivery = delivery;
    }

    public List<Object> getAdditionalDocumentReferences() {
        return additionalDocumentReferences;
    }

    public void setAdditionalDocumentReferences(List<Object> additionalDocumentReferences) {
        this.additionalDocumentReferences = additionalDocumentReferences;
    }

    public Object getSignature() {
        return signature;
    }

    public void setSignature(Object signature) {
        this.signature = signature;
    }

    /**
     * 发票类型代码
     */
    public static class InvoiceTypeCode {
        @JsonProperty("listVersionID")
        private String listVersionID = "1.1"; // 固定值

        @JsonProperty("value")
        private String value; // 01=Invoice, 02=Credit Note, 03=Debit Note, 11=Self-billed Invoice

        public String getListVersionID() {
            return listVersionID;
        }

        public void setListVersionID(String listVersionID) {
            this.listVersionID = listVersionID;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }
    }
}
