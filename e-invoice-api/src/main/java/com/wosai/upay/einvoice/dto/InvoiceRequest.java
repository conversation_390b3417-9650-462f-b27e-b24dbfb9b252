package com.wosai.upay.einvoice.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 电子发票请求DTO
 */
public class InvoiceRequest {
    
    @NotBlank(message = "买方名称不能为空")
    @JsonProperty("buyer_name")
    private String buyerName;
    
    @NotBlank(message = "买方税号不能为空")
    @JsonProperty("buyer_tax_number")
    private String buyerTaxNumber;
    
    @JsonProperty("buyer_address")
    private String buyerAddress;
    
    @JsonProperty("buyer_phone")
    private String buyerPhone;
    
    @JsonProperty("buyer_bank_account")
    private String buyerBankAccount;
    
    @NotBlank(message = "卖方名称不能为空")
    @JsonProperty("seller_name")
    private String sellerName;
    
    @NotBlank(message = "卖方税号不能为空")
    @JsonProperty("seller_tax_number")
    private String sellerTaxNumber;
    
    @JsonProperty("seller_address")
    private String sellerAddress;
    
    @JsonProperty("seller_phone")
    private String sellerPhone;
    
    @JsonProperty("seller_bank_account")
    private String sellerBankAccount;
    
    @NotNull(message = "发票明细不能为空")
    @JsonProperty("invoice_items")
    private List<InvoiceItem> invoiceItems;
    
    @JsonProperty("remarks")
    private String remarks;
    
    // Getters and Setters
    public String getBuyerName() {
        return buyerName;
    }
    
    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName;
    }
    
    public String getBuyerTaxNumber() {
        return buyerTaxNumber;
    }
    
    public void setBuyerTaxNumber(String buyerTaxNumber) {
        this.buyerTaxNumber = buyerTaxNumber;
    }
    
    public String getBuyerAddress() {
        return buyerAddress;
    }
    
    public void setBuyerAddress(String buyerAddress) {
        this.buyerAddress = buyerAddress;
    }
    
    public String getBuyerPhone() {
        return buyerPhone;
    }
    
    public void setBuyerPhone(String buyerPhone) {
        this.buyerPhone = buyerPhone;
    }
    
    public String getBuyerBankAccount() {
        return buyerBankAccount;
    }
    
    public void setBuyerBankAccount(String buyerBankAccount) {
        this.buyerBankAccount = buyerBankAccount;
    }
    
    public String getSellerName() {
        return sellerName;
    }
    
    public void setSellerName(String sellerName) {
        this.sellerName = sellerName;
    }
    
    public String getSellerTaxNumber() {
        return sellerTaxNumber;
    }
    
    public void setSellerTaxNumber(String sellerTaxNumber) {
        this.sellerTaxNumber = sellerTaxNumber;
    }
    
    public String getSellerAddress() {
        return sellerAddress;
    }
    
    public void setSellerAddress(String sellerAddress) {
        this.sellerAddress = sellerAddress;
    }
    
    public String getSellerPhone() {
        return sellerPhone;
    }
    
    public void setSellerPhone(String sellerPhone) {
        this.sellerPhone = sellerPhone;
    }
    
    public String getSellerBankAccount() {
        return sellerBankAccount;
    }
    
    public void setSellerBankAccount(String sellerBankAccount) {
        this.sellerBankAccount = sellerBankAccount;
    }
    
    public List<InvoiceItem> getInvoiceItems() {
        return invoiceItems;
    }
    
    public void setInvoiceItems(List<InvoiceItem> invoiceItems) {
        this.invoiceItems = invoiceItems;
    }
    
    public String getRemarks() {
        return remarks;
    }
    
    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }
}
