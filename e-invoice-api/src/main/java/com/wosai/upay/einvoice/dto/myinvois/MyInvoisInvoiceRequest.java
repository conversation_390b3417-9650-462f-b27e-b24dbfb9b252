package com.wosai.upay.einvoice.dto.myinvois;

import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * MyInvois发票提交请求DTO
 * 基于MyInvois API规范
 */
public class MyInvoisInvoiceRequest {
    
    @NotNull(message = "documents不能为空")
    @Size(min = 1, max = 100, message = "每次提交最多100个文档")
    @JsonProperty("documents")
    private List<@Valid DocumentSubmission> documents;
    
    // Getters and Setters
    public List<DocumentSubmission> getDocuments() {
        return documents;
    }
    
    public void setDocuments(List<DocumentSubmission> documents) {
        this.documents = documents;
    }
    
    /**
     * 单个文档提交结构
     */
    public static class DocumentSubmission {
        
        @NotBlank(message = "format不能为空")
        @JsonProperty("format")
        private String format; // XML 或 JSON
        
        @NotBlank(message = "document不能为空")
        @JsonProperty("document")
        private String document; // Base64编码的文档内容
        
        @NotBlank(message = "documentHash不能为空")
        @JsonProperty("documentHash")
        private String documentHash; // SHA256哈希值
        
        @NotBlank(message = "codeNumber不能为空")
        @Size(max = 50, message = "codeNumber最大长度为50")
        @JsonProperty("codeNumber")
        private String codeNumber; // 供应商内部跟踪号
        
        // Getters and Setters
        public String getFormat() {
            return format;
        }
        
        public void setFormat(String format) {
            this.format = format;
        }
        
        public String getDocument() {
            return document;
        }
        
        public void setDocument(String document) {
            this.document = document;
        }
        
        public String getDocumentHash() {
            return documentHash;
        }
        
        public void setDocumentHash(String documentHash) {
            this.documentHash = documentHash;
        }
        
        public String getCodeNumber() {
            return codeNumber;
        }
        
        public void setCodeNumber(String codeNumber) {
            this.codeNumber = codeNumber;
        }
    }
}
