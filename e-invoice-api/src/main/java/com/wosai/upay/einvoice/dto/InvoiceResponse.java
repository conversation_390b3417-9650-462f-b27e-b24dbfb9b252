package com.wosai.upay.einvoice.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 电子发票响应DTO
 */
public class InvoiceResponse {
    
    @JsonProperty("invoice_id")
    private String invoiceId;
    
    @JsonProperty("invoice_number")
    private String invoiceNumber;
    
    @JsonProperty("invoice_code")
    private String invoiceCode;
    
    @JsonProperty("issue_date")
    private LocalDateTime issueDate;
    
    @JsonProperty("total_amount")
    private BigDecimal totalAmount;
    
    @JsonProperty("tax_amount")
    private BigDecimal taxAmount;
    
    @JsonProperty("amount_including_tax")
    private BigDecimal amountIncludingTax;
    
    @JsonProperty("status")
    private String status;
    
    @JsonProperty("pdf_url")
    private String pdfUrl;
    
    @JsonProperty("xml_url")
    private String xmlUrl;
    
    @JsonProperty("qr_code")
    private String qrCode;
    
    @JsonProperty("verification_code")
    private String verificationCode;
    
    @JsonProperty("machine_number")
    private String machineNumber;
    
    // Getters and Setters
    public String getInvoiceId() {
        return invoiceId;
    }
    
    public void setInvoiceId(String invoiceId) {
        this.invoiceId = invoiceId;
    }
    
    public String getInvoiceNumber() {
        return invoiceNumber;
    }
    
    public void setInvoiceNumber(String invoiceNumber) {
        this.invoiceNumber = invoiceNumber;
    }
    
    public String getInvoiceCode() {
        return invoiceCode;
    }
    
    public void setInvoiceCode(String invoiceCode) {
        this.invoiceCode = invoiceCode;
    }
    
    public LocalDateTime getIssueDate() {
        return issueDate;
    }
    
    public void setIssueDate(LocalDateTime issueDate) {
        this.issueDate = issueDate;
    }
    
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }
    
    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }
    
    public BigDecimal getTaxAmount() {
        return taxAmount;
    }
    
    public void setTaxAmount(BigDecimal taxAmount) {
        this.taxAmount = taxAmount;
    }
    
    public BigDecimal getAmountIncludingTax() {
        return amountIncludingTax;
    }
    
    public void setAmountIncludingTax(BigDecimal amountIncludingTax) {
        this.amountIncludingTax = amountIncludingTax;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getPdfUrl() {
        return pdfUrl;
    }
    
    public void setPdfUrl(String pdfUrl) {
        this.pdfUrl = pdfUrl;
    }
    
    public String getXmlUrl() {
        return xmlUrl;
    }
    
    public void setXmlUrl(String xmlUrl) {
        this.xmlUrl = xmlUrl;
    }
    
    public String getQrCode() {
        return qrCode;
    }
    
    public void setQrCode(String qrCode) {
        this.qrCode = qrCode;
    }
    
    public String getVerificationCode() {
        return verificationCode;
    }
    
    public void setVerificationCode(String verificationCode) {
        this.verificationCode = verificationCode;
    }
    
    public String getMachineNumber() {
        return machineNumber;
    }
    
    public void setMachineNumber(String machineNumber) {
        this.machineNumber = machineNumber;
    }
}
