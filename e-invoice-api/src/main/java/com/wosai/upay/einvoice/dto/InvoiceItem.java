package com.wosai.upay.einvoice.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.math.BigDecimal;

/**
 * 发票明细项DTO
 */
public class InvoiceItem {
    
    @NotBlank(message = "商品名称不能为空")
    @JsonProperty("item_name")
    private String itemName;
    
    @JsonProperty("specification")
    private String specification;
    
    @JsonProperty("unit")
    private String unit;
    
    @NotNull(message = "数量不能为空")
    @Positive(message = "数量必须大于0")
    @JsonProperty("quantity")
    private BigDecimal quantity;
    
    @NotNull(message = "单价不能为空")
    @Positive(message = "单价必须大于0")
    @JsonProperty("unit_price")
    private BigDecimal unitPrice;
    
    @NotNull(message = "金额不能为空")
    @JsonProperty("amount")
    private BigDecimal amount;
    
    @JsonProperty("tax_rate")
    private BigDecimal taxRate;
    
    @JsonProperty("tax_amount")
    private BigDecimal taxAmount;
    
    // Getters and Setters
    public String getItemName() {
        return itemName;
    }
    
    public void setItemName(String itemName) {
        this.itemName = itemName;
    }
    
    public String getSpecification() {
        return specification;
    }
    
    public void setSpecification(String specification) {
        this.specification = specification;
    }
    
    public String getUnit() {
        return unit;
    }
    
    public void setUnit(String unit) {
        this.unit = unit;
    }
    
    public BigDecimal getQuantity() {
        return quantity;
    }
    
    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }
    
    public BigDecimal getUnitPrice() {
        return unitPrice;
    }
    
    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }
    
    public BigDecimal getAmount() {
        return amount;
    }
    
    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }
    
    public BigDecimal getTaxRate() {
        return taxRate;
    }
    
    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }
    
    public BigDecimal getTaxAmount() {
        return taxAmount;
    }
    
    public void setTaxAmount(BigDecimal taxAmount) {
        this.taxAmount = taxAmount;
    }
}
