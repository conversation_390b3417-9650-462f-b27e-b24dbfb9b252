package com.wosai.upay.einvoice.dto.ubl;

import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * UBL Party (当事方) 结构
 */
public class Party {
    
    @Valid
    @NotNull(message = "当事方标识不能为空")
    @JsonProperty("PartyIdentification")
    private List<PartyIdentification> partyIdentifications;
    
    @Valid
    @JsonProperty("PartyLegalEntity")
    private PartyLegalEntity partyLegalEntity;
    
    @Valid
    @JsonProperty("PostalAddress")
    private PostalAddress postalAddress;
    
    @Valid
    @JsonProperty("Contact")
    private Contact contact;
    
    @JsonProperty("IndustryClassificationCode")
    private IndustryClassificationCode industryClassificationCode;
    
    // Getters and Setters
    public List<PartyIdentification> getPartyIdentifications() {
        return partyIdentifications;
    }
    
    public void setPartyIdentifications(List<PartyIdentification> partyIdentifications) {
        this.partyIdentifications = partyIdentifications;
    }
    
    public PartyLegalEntity getPartyLegalEntity() {
        return partyLegalEntity;
    }
    
    public void setPartyLegalEntity(PartyLegalEntity partyLegalEntity) {
        this.partyLegalEntity = partyLegalEntity;
    }
    
    public PostalAddress getPostalAddress() {
        return postalAddress;
    }
    
    public void setPostalAddress(PostalAddress postalAddress) {
        this.postalAddress = postalAddress;
    }
    
    public Contact getContact() {
        return contact;
    }
    
    public void setContact(Contact contact) {
        this.contact = contact;
    }
    
    public IndustryClassificationCode getIndustryClassificationCode() {
        return industryClassificationCode;
    }
    
    public void setIndustryClassificationCode(IndustryClassificationCode industryClassificationCode) {
        this.industryClassificationCode = industryClassificationCode;
    }
    
    /**
     * 当事方标识
     */
    public static class PartyIdentification {
        @JsonProperty("ID")
        private ID id;
        
        public ID getId() {
            return id;
        }
        
        public void setId(ID id) {
            this.id = id;
        }
        
        public static class ID {
            @JsonProperty("schemeID")
            private String schemeID; // TIN, BRN, NRIC, PASSPORT, ARMY, SST, TTX
            
            @JsonProperty("value")
            private String value;
            
            public String getSchemeID() {
                return schemeID;
            }
            
            public void setSchemeID(String schemeID) {
                this.schemeID = schemeID;
            }
            
            public String getValue() {
                return value;
            }
            
            public void setValue(String value) {
                this.value = value;
            }
        }
    }
    
    /**
     * 当事方法律实体
     */
    public static class PartyLegalEntity {
        @NotBlank(message = "注册名称不能为空")
        @JsonProperty("RegistrationName")
        private String registrationName;
        
        public String getRegistrationName() {
            return registrationName;
        }
        
        public void setRegistrationName(String registrationName) {
            this.registrationName = registrationName;
        }
    }
    
    /**
     * 邮政地址
     */
    public static class PostalAddress {
        @JsonProperty("AddressLine")
        private List<AddressLine> addressLines;
        
        @JsonProperty("PostalZone")
        private String postalZone;
        
        @NotBlank(message = "城市名称不能为空")
        @JsonProperty("CityName")
        private String cityName;
        
        @NotBlank(message = "州代码不能为空")
        @JsonProperty("CountrySubentityCode")
        private String countrySubentityCode; // 州代码
        
        @JsonProperty("Country")
        private Country country;
        
        public List<AddressLine> getAddressLines() {
            return addressLines;
        }
        
        public void setAddressLines(List<AddressLine> addressLines) {
            this.addressLines = addressLines;
        }
        
        public String getPostalZone() {
            return postalZone;
        }
        
        public void setPostalZone(String postalZone) {
            this.postalZone = postalZone;
        }
        
        public String getCityName() {
            return cityName;
        }
        
        public void setCityName(String cityName) {
            this.cityName = cityName;
        }
        
        public String getCountrySubentityCode() {
            return countrySubentityCode;
        }
        
        public void setCountrySubentityCode(String countrySubentityCode) {
            this.countrySubentityCode = countrySubentityCode;
        }
        
        public Country getCountry() {
            return country;
        }
        
        public void setCountry(Country country) {
            this.country = country;
        }
        
        public static class AddressLine {
            @JsonProperty("Line")
            private String line;
            
            public String getLine() {
                return line;
            }
            
            public void setLine(String line) {
                this.line = line;
            }
        }
        
        public static class Country {
            @JsonProperty("IdentificationCode")
            private IdentificationCode identificationCode;
            
            public IdentificationCode getIdentificationCode() {
                return identificationCode;
            }
            
            public void setIdentificationCode(IdentificationCode identificationCode) {
                this.identificationCode = identificationCode;
            }
            
            public static class IdentificationCode {
                @JsonProperty("listID")
                private String listID = "ISO3166-1";
                
                @JsonProperty("listAgencyID")
                private String listAgencyID = "6";
                
                @JsonProperty("value")
                private String value = "MYS"; // 默认马来西亚
                
                public String getListID() {
                    return listID;
                }
                
                public void setListID(String listID) {
                    this.listID = listID;
                }
                
                public String getListAgencyID() {
                    return listAgencyID;
                }
                
                public void setListAgencyID(String listAgencyID) {
                    this.listAgencyID = listAgencyID;
                }
                
                public String getValue() {
                    return value;
                }
                
                public void setValue(String value) {
                    this.value = value;
                }
            }
        }
    }
    
    /**
     * 联系信息
     */
    public static class Contact {
        @JsonProperty("Telephone")
        private String telephone;
        
        @JsonProperty("ElectronicMail")
        private String electronicMail;
        
        public String getTelephone() {
            return telephone;
        }
        
        public void setTelephone(String telephone) {
            this.telephone = telephone;
        }
        
        public String getElectronicMail() {
            return electronicMail;
        }
        
        public void setElectronicMail(String electronicMail) {
            this.electronicMail = electronicMail;
        }
    }
    
    /**
     * 行业分类代码
     */
    public static class IndustryClassificationCode {
        @JsonProperty("name")
        private String name; // 业务活动描述
        
        @JsonProperty("value")
        private String value; // MSIC代码
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public String getValue() {
            return value;
        }
        
        public void setValue(String value) {
            this.value = value;
        }
    }
}
