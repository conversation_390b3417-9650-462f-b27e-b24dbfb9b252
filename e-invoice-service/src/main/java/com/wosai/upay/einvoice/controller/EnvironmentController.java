package com.wosai.upay.einvoice.controller;

import com.wosai.upay.einvoice.dto.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 环境配置信息控制器
 * 提供当前环境配置信息查询功能
 */
@RestController
@RequestMapping("/api/v1/env")
@CrossOrigin(origins = "*")
public class EnvironmentController {
    
    private static final Logger logger = LoggerFactory.getLogger(EnvironmentController.class);
    
    @Autowired
    private Environment environment;
    
    @Value("${spring.application.name}")
    private String applicationName;
    
    @Value("${myinvois.api.base-url}")
    private String myInvoisApiUrl;
    
    @Value("${myinvois.auth.base-url}")
    private String myInvoisAuthUrl;
    
    @Value("${spring.datasource.url}")
    private String datasourceUrl;
    
    @Value("${spring.redis.host}")
    private String redisHost;
    
    @Value("${spring.redis.database}")
    private int redisDatabase;
    
    /**
     * 获取当前环境信息
     */
    @GetMapping("/info")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getEnvironmentInfo() {
        logger.info("获取环境配置信息");
        
        Map<String, Object> envInfo = new HashMap<>();
        
        try {
            // 基本信息
            envInfo.put("application_name", applicationName);
            envInfo.put("active_profiles", Arrays.asList(environment.getActiveProfiles()));
            envInfo.put("default_profiles", Arrays.asList(environment.getDefaultProfiles()));
            
            // 当前激活的环境
            String[] activeProfiles = environment.getActiveProfiles();
            String currentProfile = activeProfiles.length > 0 ? activeProfiles[0] : "default";
            envInfo.put("current_environment", currentProfile);
            
            // MyInvois配置
            Map<String, Object> myInvoisConfig = new HashMap<>();
            myInvoisConfig.put("api_url", myInvoisApiUrl);
            myInvoisConfig.put("auth_url", myInvoisAuthUrl);
            myInvoisConfig.put("is_sandbox", myInvoisApiUrl.contains("sandbox"));
            envInfo.put("myinvois_config", myInvoisConfig);
            
            // 数据库配置
            Map<String, Object> dbConfig = new HashMap<>();
            dbConfig.put("url", maskSensitiveInfo(datasourceUrl));
            dbConfig.put("type", datasourceUrl.contains("h2") ? "H2" : "MySQL");
            envInfo.put("database_config", dbConfig);
            
            // Redis配置
            Map<String, Object> redisConfig = new HashMap<>();
            redisConfig.put("host", redisHost);
            redisConfig.put("database", redisDatabase);
            envInfo.put("redis_config", redisConfig);
            
            // 环境特性
            Map<String, Object> features = new HashMap<>();
            features.put("h2_console_enabled", environment.getProperty("spring.h2.console.enabled", Boolean.class, false));
            features.put("sql_logging_enabled", environment.getProperty("spring.jpa.show-sql", Boolean.class, false));
            features.put("debug_logging", isDebugLoggingEnabled());
            envInfo.put("features", features);
            
            return ResponseEntity.ok(ApiResponse.success("环境信息获取成功", envInfo));
            
        } catch (Exception e) {
            logger.error("获取环境信息失败", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(400, "获取环境信息失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取环境健康状态
     */
    @GetMapping("/health")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getEnvironmentHealth() {
        logger.info("检查环境健康状态");
        
        Map<String, Object> healthInfo = new HashMap<>();
        
        try {
            String[] activeProfiles = environment.getActiveProfiles();
            String currentProfile = activeProfiles.length > 0 ? activeProfiles[0] : "default";
            
            healthInfo.put("environment", currentProfile);
            healthInfo.put("status", "UP");
            healthInfo.put("timestamp", System.currentTimeMillis());
            
            // 检查关键配置
            Map<String, Object> configCheck = new HashMap<>();
            configCheck.put("myinvois_configured", !myInvoisApiUrl.contains("your-client-id"));
            configCheck.put("database_configured", !datasourceUrl.isEmpty());
            configCheck.put("redis_configured", !redisHost.isEmpty());
            healthInfo.put("configuration_status", configCheck);
            
            // 环境特定检查
            switch (currentProfile) {
                case "default":
                    healthInfo.put("environment_type", "开发环境");
                    healthInfo.put("warnings", Arrays.asList("使用H2内存数据库", "使用MyInvois沙盒环境"));
                    break;
                case "beta":
                    healthInfo.put("environment_type", "测试环境");
                    healthInfo.put("warnings", Arrays.asList("使用MyInvois沙盒环境"));
                    break;
                case "prod":
                    healthInfo.put("environment_type", "生产环境");
                    healthInfo.put("warnings", Arrays.asList());
                    break;
                default:
                    healthInfo.put("environment_type", "未知环境");
                    healthInfo.put("warnings", Arrays.asList("未识别的环境配置"));
            }
            
            return ResponseEntity.ok(ApiResponse.success("环境健康检查完成", healthInfo));
            
        } catch (Exception e) {
            logger.error("环境健康检查失败", e);
            return ResponseEntity.status(503)
                    .body(ApiResponse.error(503, "环境健康检查失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取配置属性
     */
    @GetMapping("/config/{key}")
    public ResponseEntity<ApiResponse<Object>> getConfigProperty(@PathVariable String key) {
        logger.info("获取配置属性: {}", key);
        
        try {
            // 安全检查，防止获取敏感信息
            if (isSensitiveProperty(key)) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error(400, "不允许访问敏感配置信息"));
            }
            
            String value = environment.getProperty(key);
            if (value == null) {
                return ResponseEntity.notFound().build();
            }
            
            return ResponseEntity.ok(ApiResponse.success("配置属性获取成功", value));
            
        } catch (Exception e) {
            logger.error("获取配置属性失败: " + key, e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(400, "获取配置属性失败: " + e.getMessage()));
        }
    }
    
    /**
     * 掩码敏感信息
     */
    private String maskSensitiveInfo(String value) {
        if (value == null || value.isEmpty()) {
            return value;
        }
        
        // 掩码密码等敏感信息
        if (value.contains("password=")) {
            return value.replaceAll("password=[^&;]*", "password=***");
        }
        
        return value;
    }
    
    /**
     * 检查是否启用了调试日志
     */
    private boolean isDebugLoggingEnabled() {
        String logLevel = environment.getProperty("logging.level.com.wosai.upay.einvoice");
        return "DEBUG".equalsIgnoreCase(logLevel);
    }
    
    /**
     * 检查是否为敏感属性
     */
    private boolean isSensitiveProperty(String key) {
        String lowerKey = key.toLowerCase();
        return lowerKey.contains("password") || 
               lowerKey.contains("secret") || 
               lowerKey.contains("key") ||
               lowerKey.contains("token") ||
               lowerKey.contains("credential");
    }
}
