package com.wosai.upay.einvoice.entity;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 发票实体类
 */
@Entity
@Table(name = "invoices")
public class Invoice {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "invoice_id", unique = true, nullable = false)
    private String invoiceId;
    
    @Column(name = "invoice_number", unique = true, nullable = false)
    private String invoiceNumber;
    
    @Column(name = "invoice_code", nullable = false)
    private String invoiceCode;
    
    @Column(name = "buyer_name", nullable = false)
    private String buyerName;
    
    @Column(name = "buyer_tax_number", nullable = false)
    private String buyerTaxNumber;
    
    @Column(name = "buyer_address")
    private String buyerAddress;
    
    @Column(name = "buyer_phone")
    private String buyerPhone;
    
    @Column(name = "buyer_bank_account")
    private String buyerBankAccount;
    
    @Column(name = "seller_name", nullable = false)
    private String sellerName;
    
    @Column(name = "seller_tax_number", nullable = false)
    private String sellerTaxNumber;
    
    @Column(name = "seller_address")
    private String sellerAddress;
    
    @Column(name = "seller_phone")
    private String sellerPhone;
    
    @Column(name = "seller_bank_account")
    private String sellerBankAccount;
    
    @Column(name = "total_amount", precision = 19, scale = 2)
    private BigDecimal totalAmount;
    
    @Column(name = "tax_amount", precision = 19, scale = 2)
    private BigDecimal taxAmount;
    
    @Column(name = "amount_including_tax", precision = 19, scale = 2)
    private BigDecimal amountIncludingTax;
    
    @Column(name = "issue_date")
    private LocalDateTime issueDate;
    
    @Column(name = "status")
    private String status;
    
    @Column(name = "qr_code")
    private String qrCode;
    
    @Column(name = "verification_code")
    private String verificationCode;
    
    @Column(name = "machine_number")
    private String machineNumber;
    
    @Column(name = "remarks", length = 1000)
    private String remarks;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getInvoiceId() {
        return invoiceId;
    }
    
    public void setInvoiceId(String invoiceId) {
        this.invoiceId = invoiceId;
    }
    
    public String getInvoiceNumber() {
        return invoiceNumber;
    }
    
    public void setInvoiceNumber(String invoiceNumber) {
        this.invoiceNumber = invoiceNumber;
    }
    
    public String getInvoiceCode() {
        return invoiceCode;
    }
    
    public void setInvoiceCode(String invoiceCode) {
        this.invoiceCode = invoiceCode;
    }
    
    public String getBuyerName() {
        return buyerName;
    }
    
    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName;
    }
    
    public String getBuyerTaxNumber() {
        return buyerTaxNumber;
    }
    
    public void setBuyerTaxNumber(String buyerTaxNumber) {
        this.buyerTaxNumber = buyerTaxNumber;
    }
    
    public String getBuyerAddress() {
        return buyerAddress;
    }
    
    public void setBuyerAddress(String buyerAddress) {
        this.buyerAddress = buyerAddress;
    }
    
    public String getBuyerPhone() {
        return buyerPhone;
    }
    
    public void setBuyerPhone(String buyerPhone) {
        this.buyerPhone = buyerPhone;
    }
    
    public String getBuyerBankAccount() {
        return buyerBankAccount;
    }
    
    public void setBuyerBankAccount(String buyerBankAccount) {
        this.buyerBankAccount = buyerBankAccount;
    }
    
    public String getSellerName() {
        return sellerName;
    }
    
    public void setSellerName(String sellerName) {
        this.sellerName = sellerName;
    }
    
    public String getSellerTaxNumber() {
        return sellerTaxNumber;
    }
    
    public void setSellerTaxNumber(String sellerTaxNumber) {
        this.sellerTaxNumber = sellerTaxNumber;
    }
    
    public String getSellerAddress() {
        return sellerAddress;
    }
    
    public void setSellerAddress(String sellerAddress) {
        this.sellerAddress = sellerAddress;
    }
    
    public String getSellerPhone() {
        return sellerPhone;
    }
    
    public void setSellerPhone(String sellerPhone) {
        this.sellerPhone = sellerPhone;
    }
    
    public String getSellerBankAccount() {
        return sellerBankAccount;
    }
    
    public void setSellerBankAccount(String sellerBankAccount) {
        this.sellerBankAccount = sellerBankAccount;
    }
    
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }
    
    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }
    
    public BigDecimal getTaxAmount() {
        return taxAmount;
    }
    
    public void setTaxAmount(BigDecimal taxAmount) {
        this.taxAmount = taxAmount;
    }
    
    public BigDecimal getAmountIncludingTax() {
        return amountIncludingTax;
    }
    
    public void setAmountIncludingTax(BigDecimal amountIncludingTax) {
        this.amountIncludingTax = amountIncludingTax;
    }
    
    public LocalDateTime getIssueDate() {
        return issueDate;
    }
    
    public void setIssueDate(LocalDateTime issueDate) {
        this.issueDate = issueDate;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getQrCode() {
        return qrCode;
    }
    
    public void setQrCode(String qrCode) {
        this.qrCode = qrCode;
    }
    
    public String getVerificationCode() {
        return verificationCode;
    }
    
    public void setVerificationCode(String verificationCode) {
        this.verificationCode = verificationCode;
    }
    
    public String getMachineNumber() {
        return machineNumber;
    }
    
    public void setMachineNumber(String machineNumber) {
        this.machineNumber = machineNumber;
    }
    
    public String getRemarks() {
        return remarks;
    }
    
    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
}
