package com.wosai.upay.einvoice.controller;

import com.wosai.upay.einvoice.client.MyInvoisApiClient;
import com.wosai.upay.einvoice.dto.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * MyInvois系统集成控制器
 * 提供与马来西亚电子发票系统的直接交互接口
 */
@RestController
@RequestMapping("/api/v1/myinvois")
@CrossOrigin(origins = "*")
public class MyInvoisController {
    
    private static final Logger logger = LoggerFactory.getLogger(MyInvoisController.class);
    
    @Autowired
    private MyInvoisApiClient myInvoisApiClient;
    
    /**
     * 获取文档详情
     */
    @GetMapping("/documents/{uuid}")
    public Mono<ResponseEntity<ApiResponse<Map<String, Object>>>> getDocument(@PathVariable String uuid) {
        logger.info("获取MyInvois文档详情，UUID: {}", uuid);
        
        return myInvoisApiClient.getDocument(uuid)
                .map(document -> ResponseEntity.ok(ApiResponse.success("获取文档详情成功", document)))
                .onErrorReturn(ResponseEntity.badRequest()
                        .body(ApiResponse.error(400, "获取文档详情失败")));
    }
    
    /**
     * 取消文档
     */
    @PutMapping("/documents/{uuid}/cancel")
    public Mono<ResponseEntity<ApiResponse<Map<String, Object>>>> cancelDocument(
            @PathVariable String uuid,
            @RequestParam(defaultValue = "用户取消") String reason) {
        logger.info("取消MyInvois文档，UUID: {}, 原因: {}", uuid, reason);
        
        return myInvoisApiClient.cancelDocument(uuid, reason)
                .map(result -> ResponseEntity.ok(ApiResponse.success("文档取消成功", result)))
                .onErrorReturn(ResponseEntity.badRequest()
                        .body(ApiResponse.error(400, "文档取消失败")));
    }
    
    /**
     * 获取提交状态
     */
    @GetMapping("/submissions/{submissionUid}")
    public Mono<ResponseEntity<ApiResponse<Map<String, Object>>>> getSubmissionStatus(@PathVariable String submissionUid) {
        logger.info("获取MyInvois提交状态，submissionUid: {}", submissionUid);
        
        return myInvoisApiClient.getSubmissionStatus(submissionUid)
                .map(status -> ResponseEntity.ok(ApiResponse.success("获取提交状态成功", status)))
                .onErrorReturn(ResponseEntity.badRequest()
                        .body(ApiResponse.error(400, "获取提交状态失败")));
    }
    
    /**
     * 健康检查 - 测试与MyInvois系统的连接
     */
    @GetMapping("/health")
    public Mono<ResponseEntity<ApiResponse<String>>> healthCheck() {
        logger.info("MyInvois系统健康检查");
        
        // 这里可以实现一个简单的健康检查，比如获取系统状态
        return Mono.just(ResponseEntity.ok(ApiResponse.success("MyInvois集成服务正常", "Connected to MyInvois API")));
    }
}
