package com.wosai.upay.einvoice.service.impl;

import com.wosai.upay.einvoice.dto.InvoiceItem;
import com.wosai.upay.einvoice.dto.InvoiceRequest;
import com.wosai.upay.einvoice.dto.InvoiceResponse;
import com.wosai.upay.einvoice.entity.Invoice;
import com.wosai.upay.einvoice.repository.InvoiceRepository;
import com.wosai.upay.einvoice.service.InvoiceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 发票服务实现类
 */
@Service
public class InvoiceServiceImpl implements InvoiceService {
    
    @Autowired
    private InvoiceRepository invoiceRepository;
    
    @Override
    public InvoiceResponse createInvoice(InvoiceRequest request) {
        // 创建发票实体
        Invoice invoice = new Invoice();
        invoice.setInvoiceId(UUID.randomUUID().toString());
        invoice.setInvoiceNumber(generateInvoiceNumber());
        invoice.setInvoiceCode(generateInvoiceCode());
        invoice.setBuyerName(request.getBuyerName());
        invoice.setBuyerTaxNumber(request.getBuyerTaxNumber());
        invoice.setBuyerAddress(request.getBuyerAddress());
        invoice.setBuyerPhone(request.getBuyerPhone());
        invoice.setBuyerBankAccount(request.getBuyerBankAccount());
        invoice.setSellerName(request.getSellerName());
        invoice.setSellerTaxNumber(request.getSellerTaxNumber());
        invoice.setSellerAddress(request.getSellerAddress());
        invoice.setSellerPhone(request.getSellerPhone());
        invoice.setSellerBankAccount(request.getSellerBankAccount());
        invoice.setRemarks(request.getRemarks());
        invoice.setIssueDate(LocalDateTime.now());
        invoice.setStatus("ISSUED");
        
        // 计算金额
        BigDecimal totalAmount = calculateTotalAmount(request.getInvoiceItems());
        BigDecimal taxAmount = calculateTaxAmount(request.getInvoiceItems());
        BigDecimal amountIncludingTax = totalAmount.add(taxAmount);
        
        invoice.setTotalAmount(totalAmount);
        invoice.setTaxAmount(taxAmount);
        invoice.setAmountIncludingTax(amountIncludingTax);
        
        // 生成验证码和二维码
        invoice.setVerificationCode(generateVerificationCode());
        invoice.setQrCode(generateQrCode(invoice));
        invoice.setMachineNumber("001");
        
        // 保存到数据库
        invoice = invoiceRepository.save(invoice);
        
        // 转换为响应对象
        return convertToResponse(invoice);
    }
    
    @Override
    public InvoiceResponse getInvoiceById(String invoiceId) {
        Invoice invoice = invoiceRepository.findByInvoiceId(invoiceId);
        return invoice != null ? convertToResponse(invoice) : null;
    }
    
    @Override
    public List<InvoiceResponse> getInvoices(int page, int size, String status) {
        Pageable pageable = PageRequest.of(page, size);
        Page<Invoice> invoicePage;
        
        if (status != null && !status.isEmpty()) {
            invoicePage = invoiceRepository.findByStatus(status, pageable);
        } else {
            invoicePage = invoiceRepository.findAll(pageable);
        }
        
        return invoicePage.getContent().stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }
    
    @Override
    public void cancelInvoice(String invoiceId) {
        Invoice invoice = invoiceRepository.findByInvoiceId(invoiceId);
        if (invoice == null) {
            throw new RuntimeException("发票不存在");
        }
        
        if ("CANCELLED".equals(invoice.getStatus())) {
            throw new RuntimeException("发票已经被取消");
        }
        
        invoice.setStatus("CANCELLED");
        invoiceRepository.save(invoice);
    }
    
    private InvoiceResponse convertToResponse(Invoice invoice) {
        InvoiceResponse response = new InvoiceResponse();
        response.setInvoiceId(invoice.getInvoiceId());
        response.setInvoiceNumber(invoice.getInvoiceNumber());
        response.setInvoiceCode(invoice.getInvoiceCode());
        response.setIssueDate(invoice.getIssueDate());
        response.setTotalAmount(invoice.getTotalAmount());
        response.setTaxAmount(invoice.getTaxAmount());
        response.setAmountIncludingTax(invoice.getAmountIncludingTax());
        response.setStatus(invoice.getStatus());
        response.setPdfUrl("/api/v1/invoices/" + invoice.getInvoiceId() + "/pdf");
        response.setXmlUrl("/api/v1/invoices/" + invoice.getInvoiceId() + "/xml");
        response.setQrCode(invoice.getQrCode());
        response.setVerificationCode(invoice.getVerificationCode());
        response.setMachineNumber(invoice.getMachineNumber());
        return response;
    }
    
    private BigDecimal calculateTotalAmount(List<InvoiceItem> items) {
        return items.stream()
                .map(InvoiceItem::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
    
    private BigDecimal calculateTaxAmount(List<InvoiceItem> items) {
        return items.stream()
                .map(item -> item.getTaxAmount() != null ? item.getTaxAmount() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
    
    private String generateInvoiceNumber() {
        return "INV" + System.currentTimeMillis();
    }
    
    private String generateInvoiceCode() {
        return "CODE" + System.currentTimeMillis();
    }
    
    private String generateVerificationCode() {
        return String.valueOf((int) (Math.random() * 900000) + 100000);
    }
    
    private String generateQrCode(Invoice invoice) {
        return "QR_" + invoice.getInvoiceId();
    }
}
