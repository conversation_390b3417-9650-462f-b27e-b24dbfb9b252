package com.wosai.upay.einvoice.service.impl;

import com.wosai.upay.einvoice.client.MyInvoisApiClient;
import com.wosai.upay.einvoice.dto.InvoiceItem;
import com.wosai.upay.einvoice.dto.InvoiceRequest;
import com.wosai.upay.einvoice.dto.InvoiceResponse;
import com.wosai.upay.einvoice.dto.myinvois.MyInvoisInvoiceRequest;
import com.wosai.upay.einvoice.dto.myinvois.MyInvoisInvoiceResponse;
import com.wosai.upay.einvoice.dto.ubl.UBLInvoice;
import com.wosai.upay.einvoice.entity.Invoice;
import com.wosai.upay.einvoice.repository.InvoiceRepository;
import com.wosai.upay.einvoice.service.InvoiceService;
import com.wosai.upay.einvoice.service.UBLDocumentService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 发票服务实现类
 */
@Service
public class InvoiceServiceImpl implements InvoiceService {

    private static final Logger logger = LoggerFactory.getLogger(InvoiceServiceImpl.class);

    @Autowired
    private InvoiceRepository invoiceRepository;

    @Autowired
    private MyInvoisApiClient myInvoisApiClient;

    @Autowired
    private UBLDocumentService ublDocumentService;

    @Override
    public InvoiceResponse createInvoice(InvoiceRequest request) {
        logger.info("开始创建发票，买方: {}", request.getBuyerName());

        try {
            // 创建发票实体
            Invoice invoice = new Invoice();
            invoice.setInvoiceId(UUID.randomUUID().toString());
            invoice.setInvoiceNumber(generateInvoiceNumber());
            invoice.setInvoiceCode(generateInvoiceCode());
            invoice.setBuyerName(request.getBuyerName());
            invoice.setBuyerTaxNumber(request.getBuyerTaxNumber());
            invoice.setBuyerAddress(request.getBuyerAddress());
            invoice.setBuyerPhone(request.getBuyerPhone());
            invoice.setBuyerBankAccount(request.getBuyerBankAccount());
            invoice.setSellerName(request.getSellerName());
            invoice.setSellerTaxNumber(request.getSellerTaxNumber());
            invoice.setSellerAddress(request.getSellerAddress());
            invoice.setSellerPhone(request.getSellerPhone());
            invoice.setSellerBankAccount(request.getSellerBankAccount());
            invoice.setRemarks(request.getRemarks());
            invoice.setIssueDate(LocalDateTime.now());
            invoice.setStatus("PENDING"); // 初始状态为待处理

            // 计算金额
            BigDecimal totalAmount = calculateTotalAmount(request.getInvoiceItems());
            BigDecimal taxAmount = calculateTaxAmount(request.getInvoiceItems());
            BigDecimal amountIncludingTax = totalAmount.add(taxAmount);

            invoice.setTotalAmount(totalAmount);
            invoice.setTaxAmount(taxAmount);
            invoice.setAmountIncludingTax(amountIncludingTax);

            // 生成验证码和二维码
            invoice.setVerificationCode(generateVerificationCode());
            invoice.setQrCode(generateQrCode(invoice));
            invoice.setMachineNumber("001");

            // 保存到数据库
            invoice = invoiceRepository.save(invoice);
            logger.info("发票已保存到数据库，ID: {}", invoice.getInvoiceId());

            // 提交到MyInvois系统
            submitToMyInvois(invoice, request);

            // 转换为响应对象
            return convertToResponse(invoice);

        } catch (Exception e) {
            logger.error("创建发票失败", e);
            throw new RuntimeException("创建发票失败: " + e.getMessage(), e);
        }
    }

    @Override
    public InvoiceResponse getInvoiceById(String invoiceId) {
        Invoice invoice = invoiceRepository.findByInvoiceId(invoiceId);
        return invoice != null ? convertToResponse(invoice) : null;
    }

    @Override
    public List<InvoiceResponse> getInvoices(int page, int size, String status) {
        Pageable pageable = PageRequest.of(page, size);
        Page<Invoice> invoicePage;

        if (status != null && !status.isEmpty()) {
            invoicePage = invoiceRepository.findByStatus(status, pageable);
        } else {
            invoicePage = invoiceRepository.findAll(pageable);
        }

        return invoicePage.getContent().stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public void cancelInvoice(String invoiceId) {
        Invoice invoice = invoiceRepository.findByInvoiceId(invoiceId);
        if (invoice == null) {
            throw new RuntimeException("发票不存在");
        }

        if ("CANCELLED".equals(invoice.getStatus())) {
            throw new RuntimeException("发票已经被取消");
        }

        invoice.setStatus("CANCELLED");
        invoiceRepository.save(invoice);
    }

    private InvoiceResponse convertToResponse(Invoice invoice) {
        InvoiceResponse response = new InvoiceResponse();
        response.setInvoiceId(invoice.getInvoiceId());
        response.setInvoiceNumber(invoice.getInvoiceNumber());
        response.setInvoiceCode(invoice.getInvoiceCode());
        response.setIssueDate(invoice.getIssueDate());
        response.setTotalAmount(invoice.getTotalAmount());
        response.setTaxAmount(invoice.getTaxAmount());
        response.setAmountIncludingTax(invoice.getAmountIncludingTax());
        response.setStatus(invoice.getStatus());
        response.setPdfUrl("/api/v1/invoices/" + invoice.getInvoiceId() + "/pdf");
        response.setXmlUrl("/api/v1/invoices/" + invoice.getInvoiceId() + "/xml");
        response.setQrCode(invoice.getQrCode());
        response.setVerificationCode(invoice.getVerificationCode());
        response.setMachineNumber(invoice.getMachineNumber());
        return response;
    }

    private BigDecimal calculateTotalAmount(List<InvoiceItem> items) {
        return items.stream()
                .map(InvoiceItem::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private BigDecimal calculateTaxAmount(List<InvoiceItem> items) {
        return items.stream()
                .map(item -> item.getTaxAmount() != null ? item.getTaxAmount() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private String generateInvoiceNumber() {
        return "INV" + System.currentTimeMillis();
    }

    private String generateInvoiceCode() {
        return "CODE" + System.currentTimeMillis();
    }

    private String generateVerificationCode() {
        return String.valueOf((int) (Math.random() * 900000) + 100000);
    }

    private String generateQrCode(Invoice invoice) {
        return "QR_" + invoice.getInvoiceId();
    }

    /**
     * 提交发票到MyInvois系统
     */
    private void submitToMyInvois(Invoice invoice, InvoiceRequest request) {
        try {
            logger.info("开始提交发票到MyInvois系统，发票号: {}", invoice.getInvoiceNumber());

            // 转换为UBL格式
            UBLInvoice ublInvoice = ublDocumentService.convertToUBLInvoice(request, invoice.getInvoiceNumber());

            // 验证UBL文档
            if (!ublDocumentService.validateUBLDocument(ublInvoice)) {
                throw new RuntimeException("UBL文档验证失败");
            }

            // 转换为JSON格式
            String jsonContent = ublDocumentService.convertToJson(ublInvoice);

            // 编码为Base64
            String encodedDocument = ublDocumentService.encodeToBase64(jsonContent);

            // 计算哈希值
            String documentHash = ublDocumentService.calculateSHA256Hash(jsonContent);

            // 创建提交请求
            MyInvoisInvoiceRequest.DocumentSubmission docSubmission = new MyInvoisInvoiceRequest.DocumentSubmission();
            docSubmission.setFormat("JSON");
            docSubmission.setDocument(encodedDocument);
            docSubmission.setDocumentHash(documentHash);
            docSubmission.setCodeNumber(invoice.getInvoiceNumber());

            MyInvoisInvoiceRequest myInvoisRequest = new MyInvoisInvoiceRequest();
            myInvoisRequest.setDocuments(Arrays.asList(docSubmission));

            // 提交到MyInvois
            myInvoisApiClient.submitDocuments(myInvoisRequest)
                    .subscribe(
                            response -> {
                                logger.info("MyInvois提交成功，submissionUid: {}", response.getSubmissionUid());

                                // 更新发票状态
                                if (response.getAcceptedDocuments() != null && !response.getAcceptedDocuments().isEmpty()) {
                                    invoice.setStatus("SUBMITTED");
                                    // 保存MyInvois返回的UUID
                                    String myInvoisUuid = response.getAcceptedDocuments().get(0).getUuid();
                                    invoice.setQrCode(myInvoisUuid); // 使用QrCode字段保存UUID
                                } else if (response.getRejectedDocuments() != null && !response.getRejectedDocuments().isEmpty()) {
                                    invoice.setStatus("REJECTED");
                                    String errorMessage = response.getRejectedDocuments().get(0).getError().getMessage();
                                    invoice.setRemarks(invoice.getRemarks() + "; MyInvois拒绝原因: " + errorMessage);
                                }

                                invoiceRepository.save(invoice);
                            },
                            error -> {
                                logger.error("MyInvois提交失败", error);
                                invoice.setStatus("FAILED");
                                invoice.setRemarks(invoice.getRemarks() + "; MyInvois提交失败: " + error.getMessage());
                                invoiceRepository.save(invoice);
                            }
                    );

        } catch (Exception e) {
            logger.error("提交发票到MyInvois系统失败", e);
            invoice.setStatus("FAILED");
            invoice.setRemarks(invoice.getRemarks() + "; 提交失败: " + e.getMessage());
            invoiceRepository.save(invoice);
        }
    }
}
