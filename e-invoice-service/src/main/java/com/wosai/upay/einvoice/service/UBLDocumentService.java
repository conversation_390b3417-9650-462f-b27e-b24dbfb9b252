package com.wosai.upay.einvoice.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.wosai.upay.einvoice.dto.InvoiceRequest;
import com.wosai.upay.einvoice.dto.ubl.UBLInvoice;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * UBL文档处理服务
 * 负责将内部发票格式转换为UBL 2.1标准格式
 */
@Service
public class UBLDocumentService {
    
    private static final Logger logger = LoggerFactory.getLogger(UBLDocumentService.class);
    
    private final ObjectMapper jsonMapper;
    private final XmlMapper xmlMapper;
    
    public UBLDocumentService() {
        this.jsonMapper = new ObjectMapper();
        this.xmlMapper = new XmlMapper();
        
        // 配置日期时间格式
        jsonMapper.findAndRegisterModules();
        xmlMapper.findAndRegisterModules();
    }
    
    /**
     * 将内部发票请求转换为UBL发票格式
     */
    public UBLInvoice convertToUBLInvoice(InvoiceRequest request, String invoiceNumber) {
        logger.info("开始转换发票为UBL格式，发票号: {}", invoiceNumber);
        
        UBLInvoice ublInvoice = new UBLInvoice();
        
        // 基本信息
        ublInvoice.setId(invoiceNumber);
        ublInvoice.setIssueDate(LocalDate.now());
        ublInvoice.setIssueTime(LocalTime.now());
        ublInvoice.setDocumentCurrencyCode("MYR");
        ublInvoice.setTaxCurrencyCode("MYR");
        
        // 发票类型代码
        UBLInvoice.InvoiceTypeCode typeCode = new UBLInvoice.InvoiceTypeCode();
        typeCode.setValue("01"); // 01 = Invoice
        ublInvoice.setInvoiceTypeCode(typeCode);
        
        // TODO: 实现完整的转换逻辑
        // 这里需要根据MyInvois规范实现详细的字段映射
        
        logger.info("UBL发票转换完成，发票号: {}", invoiceNumber);
        return ublInvoice;
    }
    
    /**
     * 将UBL发票转换为JSON格式
     */
    public String convertToJson(UBLInvoice ublInvoice) {
        try {
            String json = jsonMapper.writeValueAsString(ublInvoice);
            logger.debug("UBL发票已转换为JSON格式");
            return json;
        } catch (Exception e) {
            logger.error("转换UBL发票为JSON失败", e);
            throw new RuntimeException("转换UBL发票为JSON失败", e);
        }
    }
    
    /**
     * 将UBL发票转换为XML格式
     */
    public String convertToXml(UBLInvoice ublInvoice) {
        try {
            String xml = xmlMapper.writeValueAsString(ublInvoice);
            logger.debug("UBL发票已转换为XML格式");
            return xml;
        } catch (Exception e) {
            logger.error("转换UBL发票为XML失败", e);
            throw new RuntimeException("转换UBL发票为XML失败", e);
        }
    }
    
    /**
     * 将文档内容编码为Base64
     */
    public String encodeToBase64(String content) {
        byte[] encoded = Base64.encodeBase64(content.getBytes(StandardCharsets.UTF_8));
        return new String(encoded, StandardCharsets.UTF_8);
    }
    
    /**
     * 计算文档的SHA256哈希值
     */
    public String calculateSHA256Hash(String content) {
        return DigestUtils.sha256Hex(content);
    }
    
    /**
     * 验证UBL文档格式
     */
    public boolean validateUBLDocument(UBLInvoice ublInvoice) {
        try {
            // 基本验证
            if (ublInvoice.getId() == null || ublInvoice.getId().trim().isEmpty()) {
                logger.error("UBL文档验证失败：发票ID不能为空");
                return false;
            }
            
            if (ublInvoice.getIssueDate() == null) {
                logger.error("UBL文档验证失败：发票日期不能为空");
                return false;
            }
            
            if (ublInvoice.getIssueTime() == null) {
                logger.error("UBL文档验证失败：发票时间不能为空");
                return false;
            }
            
            if (ublInvoice.getInvoiceTypeCode() == null || 
                ublInvoice.getInvoiceTypeCode().getValue() == null) {
                logger.error("UBL文档验证失败：发票类型代码不能为空");
                return false;
            }
            
            // TODO: 添加更多验证规则
            
            logger.debug("UBL文档验证通过");
            return true;
        } catch (Exception e) {
            logger.error("UBL文档验证过程中发生错误", e);
            return false;
        }
    }
}
