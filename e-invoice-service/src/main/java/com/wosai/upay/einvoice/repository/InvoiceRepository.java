package com.wosai.upay.einvoice.repository;

import com.wosai.upay.einvoice.entity.Invoice;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * 发票数据访问层
 */
@Repository
public interface InvoiceRepository extends JpaRepository<Invoice, Long> {
    
    /**
     * 根据发票ID查找发票
     */
    Invoice findByInvoiceId(String invoiceId);
    
    /**
     * 根据发票号查找发票
     */
    Invoice findByInvoiceNumber(String invoiceNumber);
    
    /**
     * 根据状态查找发票
     */
    Page<Invoice> findByStatus(String status, Pageable pageable);
    
    /**
     * 根据买方税号查找发票
     */
    Page<Invoice> findByBuyerTaxNumber(String buyerTaxNumber, Pageable pageable);
    
    /**
     * 根据卖方税号查找发票
     */
    Page<Invoice> findBySellerTaxNumber(String sellerTaxNumber, Pageable pageable);
}
