package com.wosai.upay.einvoice.client;

import com.wosai.upay.einvoice.dto.myinvois.MyInvoisInvoiceRequest;
import com.wosai.upay.einvoice.dto.myinvois.MyInvoisInvoiceResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.Map;

/**
 * MyInvois API客户端
 * 负责与马来西亚电子发票系统的API交互
 */
@Component
public class MyInvoisApiClient {
    
    private static final Logger logger = LoggerFactory.getLogger(MyInvoisApiClient.class);
    
    private final WebClient webClient;
    private final MyInvoisAuthService authService;
    
    @Value("${myinvois.api.base-url:https://myinvois.hasil.gov.my}")
    private String baseUrl;
    
    @Value("${myinvois.api.timeout:30}")
    private int timeoutSeconds;
    
    public MyInvoisApiClient(WebClient.Builder webClientBuilder, MyInvoisAuthService authService) {
        this.authService = authService;
        this.webClient = webClientBuilder
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(10 * 1024 * 1024)) // 10MB
                .build();
    }
    
    /**
     * 提交发票到MyInvois系统
     */
    public Mono<MyInvoisInvoiceResponse> submitDocuments(MyInvoisInvoiceRequest request) {
        logger.info("开始提交发票到MyInvois系统，文档数量: {}", request.getDocuments().size());
        
        return authService.getAccessToken()
                .flatMap(token -> {
                    String url = baseUrl + "/api/v1.0/documentsubmissions";
                    
                    return webClient.post()
                            .uri(url)
                            .header(HttpHeaders.AUTHORIZATION, "Bearer " + token)
                            .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                            .header(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
                            .bodyValue(request)
                            .retrieve()
                            .onStatus(status -> status.is4xxClientError() || status.is5xxServerError(),
                                    response -> response.bodyToMono(String.class)
                                            .flatMap(body -> {
                                                logger.error("MyInvois API错误: {} - {}", response.statusCode(), body);
                                                return Mono.error(new RuntimeException("MyInvois API调用失败: " + body));
                                            }))
                            .bodyToMono(MyInvoisInvoiceResponse.class)
                            .timeout(Duration.ofSeconds(timeoutSeconds))
                            .doOnSuccess(response -> logger.info("发票提交成功，submissionUid: {}", response.getSubmissionUid()))
                            .doOnError(error -> logger.error("发票提交失败", error));
                });
    }
    
    /**
     * 获取文档详情
     */
    public Mono<Map<String, Object>> getDocument(String uuid) {
        logger.info("获取文档详情，UUID: {}", uuid);
        
        return authService.getAccessToken()
                .flatMap(token -> {
                    String url = baseUrl + "/api/v1.0/documents/" + uuid + "/details";
                    
                    return webClient.get()
                            .uri(url)
                            .header(HttpHeaders.AUTHORIZATION, "Bearer " + token)
                            .header(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
                            .retrieve()
                            .onStatus(status -> status.is4xxClientError() || status.is5xxServerError(),
                                    response -> response.bodyToMono(String.class)
                                            .flatMap(body -> {
                                                logger.error("获取文档详情失败: {} - {}", response.statusCode(), body);
                                                return Mono.error(new RuntimeException("获取文档详情失败: " + body));
                                            }))
                            .bodyToMono(Map.class)
                            .timeout(Duration.ofSeconds(timeoutSeconds))
                            .doOnSuccess(response -> logger.info("获取文档详情成功，UUID: {}", uuid))
                            .doOnError(error -> logger.error("获取文档详情失败，UUID: " + uuid, error));
                });
    }
    
    /**
     * 取消文档
     */
    public Mono<Map<String, Object>> cancelDocument(String uuid, String reason) {
        logger.info("取消文档，UUID: {}, 原因: {}", uuid, reason);
        
        return authService.getAccessToken()
                .flatMap(token -> {
                    String url = baseUrl + "/api/v1.0/documents/" + uuid + "/state";
                    
                    Map<String, Object> requestBody = Map.of(
                            "status", "cancelled",
                            "reason", reason
                    );
                    
                    return webClient.put()
                            .uri(url)
                            .header(HttpHeaders.AUTHORIZATION, "Bearer " + token)
                            .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                            .header(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
                            .bodyValue(requestBody)
                            .retrieve()
                            .onStatus(status -> status.is4xxClientError() || status.is5xxServerError(),
                                    response -> response.bodyToMono(String.class)
                                            .flatMap(body -> {
                                                logger.error("取消文档失败: {} - {}", response.statusCode(), body);
                                                return Mono.error(new RuntimeException("取消文档失败: " + body));
                                            }))
                            .bodyToMono(Map.class)
                            .timeout(Duration.ofSeconds(timeoutSeconds))
                            .doOnSuccess(response -> logger.info("取消文档成功，UUID: {}", uuid))
                            .doOnError(error -> logger.error("取消文档失败，UUID: " + uuid, error));
                });
    }
    
    /**
     * 获取提交状态
     */
    public Mono<Map<String, Object>> getSubmissionStatus(String submissionUid) {
        logger.info("获取提交状态，submissionUid: {}", submissionUid);
        
        return authService.getAccessToken()
                .flatMap(token -> {
                    String url = baseUrl + "/api/v1.0/documentsubmissions/" + submissionUid;
                    
                    return webClient.get()
                            .uri(url)
                            .header(HttpHeaders.AUTHORIZATION, "Bearer " + token)
                            .header(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
                            .retrieve()
                            .onStatus(status -> status.is4xxClientError() || status.is5xxServerError(),
                                    response -> response.bodyToMono(String.class)
                                            .flatMap(body -> {
                                                logger.error("获取提交状态失败: {} - {}", response.statusCode(), body);
                                                return Mono.error(new RuntimeException("获取提交状态失败: " + body));
                                            }))
                            .bodyToMono(Map.class)
                            .timeout(Duration.ofSeconds(timeoutSeconds))
                            .doOnSuccess(response -> logger.info("获取提交状态成功，submissionUid: {}", submissionUid))
                            .doOnError(error -> logger.error("获取提交状态失败，submissionUid: " + submissionUid, error));
                });
    }
}
