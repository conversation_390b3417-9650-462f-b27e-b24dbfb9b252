package com.wosai.upay.einvoice.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * Token缓存服务
 * 使用Redis缓存MyInvois访问令牌
 */
@Service
public class TokenCacheService {
    
    private static final Logger logger = LoggerFactory.getLogger(TokenCacheService.class);
    
    private static final String TOKEN_KEY_PREFIX = "myinvois:token:";
    private static final String TOKEN_EXPIRY_KEY_PREFIX = "myinvois:token:expiry:";
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 缓存访问令牌
     * 
     * @param clientId 客户端ID，用作缓存key的一部分
     * @param accessToken 访问令牌
     * @param expiresInSeconds 令牌过期时间（秒）
     */
    public void cacheAccessToken(String clientId, String accessToken, long expiresInSeconds) {
        try {
            String tokenKey = TOKEN_KEY_PREFIX + clientId;
            String expiryKey = TOKEN_EXPIRY_KEY_PREFIX + clientId;
            
            // 缓存令牌，设置过期时间比实际过期时间提前5分钟
            long cacheExpirySeconds = Math.max(expiresInSeconds - 300, 60); // 至少缓存1分钟
            
            redisTemplate.opsForValue().set(tokenKey, accessToken, cacheExpirySeconds, TimeUnit.SECONDS);
            redisTemplate.opsForValue().set(expiryKey, System.currentTimeMillis() + (expiresInSeconds * 1000), 
                    cacheExpirySeconds, TimeUnit.SECONDS);
            
            logger.info("访问令牌已缓存到Redis，客户端ID: {}, 缓存过期时间: {}秒", clientId, cacheExpirySeconds);
            
        } catch (Exception e) {
            logger.error("缓存访问令牌失败，客户端ID: " + clientId, e);
        }
    }
    
    /**
     * 获取缓存的访问令牌
     * 
     * @param clientId 客户端ID
     * @return 访问令牌，如果不存在或已过期则返回null
     */
    public String getCachedAccessToken(String clientId) {
        try {
            String tokenKey = TOKEN_KEY_PREFIX + clientId;
            String expiryKey = TOKEN_EXPIRY_KEY_PREFIX + clientId;
            
            // 检查令牌是否存在
            String accessToken = (String) redisTemplate.opsForValue().get(tokenKey);
            if (accessToken == null) {
                logger.debug("Redis中未找到访问令牌，客户端ID: {}", clientId);
                return null;
            }
            
            // 检查令牌是否过期
            Long expiryTime = (Long) redisTemplate.opsForValue().get(expiryKey);
            if (expiryTime == null || System.currentTimeMillis() >= expiryTime) {
                logger.debug("访问令牌已过期，客户端ID: {}", clientId);
                // 清除过期的令牌
                clearCachedToken(clientId);
                return null;
            }
            
            logger.debug("从Redis获取到有效的访问令牌，客户端ID: {}", clientId);
            return accessToken;
            
        } catch (Exception e) {
            logger.error("获取缓存的访问令牌失败，客户端ID: " + clientId, e);
            return null;
        }
    }
    
    /**
     * 检查令牌是否有效（存在且未过期）
     * 
     * @param clientId 客户端ID
     * @return true如果令牌有效，false否则
     */
    public boolean isTokenValid(String clientId) {
        return getCachedAccessToken(clientId) != null;
    }
    
    /**
     * 清除缓存的令牌
     * 
     * @param clientId 客户端ID
     */
    public void clearCachedToken(String clientId) {
        try {
            String tokenKey = TOKEN_KEY_PREFIX + clientId;
            String expiryKey = TOKEN_EXPIRY_KEY_PREFIX + clientId;
            
            redisTemplate.delete(tokenKey);
            redisTemplate.delete(expiryKey);
            
            logger.info("已清除缓存的访问令牌，客户端ID: {}", clientId);
            
        } catch (Exception e) {
            logger.error("清除缓存的访问令牌失败，客户端ID: " + clientId, e);
        }
    }
    
    /**
     * 获取令牌剩余有效时间（秒）
     * 
     * @param clientId 客户端ID
     * @return 剩余有效时间，如果令牌不存在或已过期则返回0
     */
    public long getTokenRemainingTime(String clientId) {
        try {
            String expiryKey = TOKEN_EXPIRY_KEY_PREFIX + clientId;
            Long expiryTime = (Long) redisTemplate.opsForValue().get(expiryKey);
            
            if (expiryTime == null) {
                return 0;
            }
            
            long remainingTime = (expiryTime - System.currentTimeMillis()) / 1000;
            return Math.max(remainingTime, 0);
            
        } catch (Exception e) {
            logger.error("获取令牌剩余时间失败，客户端ID: " + clientId, e);
            return 0;
        }
    }
    
    /**
     * 测试Redis连接
     * 
     * @return true如果连接正常，false否则
     */
    public boolean testConnection() {
        try {
            String testKey = "myinvois:test:connection";
            String testValue = "test";
            
            redisTemplate.opsForValue().set(testKey, testValue, Duration.ofSeconds(10));
            String result = (String) redisTemplate.opsForValue().get(testKey);
            redisTemplate.delete(testKey);
            
            boolean isConnected = testValue.equals(result);
            logger.info("Redis连接测试结果: {}", isConnected ? "成功" : "失败");
            return isConnected;
            
        } catch (Exception e) {
            logger.error("Redis连接测试失败", e);
            return false;
        }
    }
}
