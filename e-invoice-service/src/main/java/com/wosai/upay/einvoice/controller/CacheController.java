package com.wosai.upay.einvoice.controller;

import com.wosai.upay.einvoice.client.MyInvoisAuthService;
import com.wosai.upay.einvoice.dto.ApiResponse;
import com.wosai.upay.einvoice.service.TokenCacheService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 缓存管理控制器
 * 提供Redis缓存和Token管理功能
 */
@RestController
@RequestMapping("/api/v1/cache")
@CrossOrigin(origins = "*")
public class CacheController {
    
    private static final Logger logger = LoggerFactory.getLogger(CacheController.class);
    
    @Autowired
    private TokenCacheService tokenCacheService;
    
    @Autowired
    private MyInvoisAuthService authService;
    
    /**
     * Redis连接健康检查
     */
    @GetMapping("/health")
    public ResponseEntity<ApiResponse<Map<String, Object>>> healthCheck() {
        logger.info("执行Redis健康检查");
        
        Map<String, Object> healthInfo = new HashMap<>();
        
        try {
            boolean isConnected = tokenCacheService.testConnection();
            healthInfo.put("redis_connected", isConnected);
            healthInfo.put("status", isConnected ? "UP" : "DOWN");
            
            if (isConnected) {
                return ResponseEntity.ok(ApiResponse.success("Redis连接正常", healthInfo));
            } else {
                return ResponseEntity.status(503)
                        .body(ApiResponse.error(503, "Redis连接失败"));
            }
            
        } catch (Exception e) {
            logger.error("Redis健康检查失败", e);
            healthInfo.put("redis_connected", false);
            healthInfo.put("status", "DOWN");
            healthInfo.put("error", e.getMessage());
            
            return ResponseEntity.status(503)
                    .body(ApiResponse.error(503, "Redis健康检查失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取Token缓存状态
     */
    @GetMapping("/token/status")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getTokenStatus() {
        logger.info("获取Token缓存状态");
        
        Map<String, Object> tokenInfo = new HashMap<>();
        
        try {
            boolean isValid = authService.isTokenValid();
            long remainingTime = authService.getTokenRemainingTime();
            
            tokenInfo.put("is_valid", isValid);
            tokenInfo.put("remaining_time_seconds", remainingTime);
            tokenInfo.put("remaining_time_minutes", remainingTime / 60);
            
            return ResponseEntity.ok(ApiResponse.success("Token状态获取成功", tokenInfo));
            
        } catch (Exception e) {
            logger.error("获取Token状态失败", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(400, "获取Token状态失败: " + e.getMessage()));
        }
    }
    
    /**
     * 清除Token缓存
     */
    @DeleteMapping("/token")
    public ResponseEntity<ApiResponse<String>> clearTokenCache() {
        logger.info("清除Token缓存");
        
        try {
            authService.clearCachedToken();
            return ResponseEntity.ok(ApiResponse.success("Token缓存已清除", "缓存清除成功"));
            
        } catch (Exception e) {
            logger.error("清除Token缓存失败", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(400, "清除Token缓存失败: " + e.getMessage()));
        }
    }
    
    /**
     * 强制刷新Token
     */
    @PostMapping("/token/refresh")
    public ResponseEntity<ApiResponse<Map<String, Object>>> refreshToken() {
        logger.info("强制刷新Token");
        
        try {
            // 先清除缓存
            authService.clearCachedToken();
            
            // 获取新Token（这会自动缓存）
            return authService.getAccessToken()
                    .map(token -> {
                        Map<String, Object> result = new HashMap<>();
                        result.put("token_refreshed", true);
                        result.put("remaining_time_seconds", authService.getTokenRemainingTime());
                        
                        return ResponseEntity.ok(ApiResponse.success("Token刷新成功", result));
                    })
                    .onErrorReturn(ResponseEntity.badRequest()
                            .body(ApiResponse.error(400, "Token刷新失败")))
                    .block(); // 注意：在实际生产环境中，建议使用响应式方式
                    
        } catch (Exception e) {
            logger.error("强制刷新Token失败", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(400, "强制刷新Token失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取缓存统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getCacheStats() {
        logger.info("获取缓存统计信息");
        
        Map<String, Object> stats = new HashMap<>();
        
        try {
            boolean redisConnected = tokenCacheService.testConnection();
            boolean tokenValid = authService.isTokenValid();
            long tokenRemainingTime = authService.getTokenRemainingTime();
            
            stats.put("redis_connected", redisConnected);
            stats.put("token_valid", tokenValid);
            stats.put("token_remaining_seconds", tokenRemainingTime);
            stats.put("token_remaining_minutes", tokenRemainingTime / 60);
            stats.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(ApiResponse.success("缓存统计信息获取成功", stats));
            
        } catch (Exception e) {
            logger.error("获取缓存统计信息失败", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(400, "获取缓存统计信息失败: " + e.getMessage()));
        }
    }
}
