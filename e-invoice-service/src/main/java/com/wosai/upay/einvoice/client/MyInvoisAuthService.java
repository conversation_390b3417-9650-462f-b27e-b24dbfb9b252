package com.wosai.upay.einvoice.client;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.Map;

/**
 * MyInvois OAuth2认证服务
 * 负责获取和管理访问令牌
 */
@Service
public class MyInvoisAuthService {
    
    private static final Logger logger = LoggerFactory.getLogger(MyInvoisAuthService.class);
    
    private final WebClient webClient;
    
    @Value("${myinvois.auth.base-url:https://myinvois.hasil.gov.my}")
    private String authBaseUrl;
    
    @Value("${myinvois.auth.client-id}")
    private String clientId;
    
    @Value("${myinvois.auth.client-secret}")
    private String clientSecret;
    
    @Value("${myinvois.auth.scope:InvoicingAPI}")
    private String scope;
    
    @Value("${myinvois.auth.timeout:30}")
    private int timeoutSeconds;
    
    // 缓存访问令牌
    private String cachedAccessToken;
    private LocalDateTime tokenExpiryTime;
    
    public MyInvoisAuthService(WebClient.Builder webClientBuilder) {
        this.webClient = webClientBuilder.build();
    }
    
    /**
     * 获取访问令牌
     * 如果缓存的令牌未过期，则返回缓存的令牌；否则获取新令牌
     */
    public Mono<String> getAccessToken() {
        if (isTokenValid()) {
            logger.debug("使用缓存的访问令牌");
            return Mono.just(cachedAccessToken);
        }
        
        logger.info("获取新的访问令牌");
        return requestNewAccessToken()
                .doOnSuccess(this::cacheToken)
                .doOnError(error -> logger.error("获取访问令牌失败", error));
    }
    
    /**
     * 请求新的访问令牌
     */
    private Mono<String> requestNewAccessToken() {
        String url = authBaseUrl + "/connect/token";
        
        // 准备请求体
        MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
        formData.add("grant_type", "client_credentials");
        formData.add("client_id", clientId);
        formData.add("client_secret", clientSecret);
        formData.add("scope", scope);
        
        // 准备Basic认证头
        String credentials = clientId + ":" + clientSecret;
        String encodedCredentials = Base64.getEncoder().encodeToString(credentials.getBytes());
        
        return webClient.post()
                .uri(url)
                .header(HttpHeaders.AUTHORIZATION, "Basic " + encodedCredentials)
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE)
                .header(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
                .body(BodyInserters.fromFormData(formData))
                .retrieve()
                .onStatus(status -> status.is4xxClientError() || status.is5xxServerError(),
                        response -> response.bodyToMono(String.class)
                                .flatMap(body -> {
                                    logger.error("OAuth2认证失败: {} - {}", response.statusCode(), body);
                                    return Mono.error(new RuntimeException("OAuth2认证失败: " + body));
                                }))
                .bodyToMono(Map.class)
                .timeout(Duration.ofSeconds(timeoutSeconds))
                .map(response -> {
                    String accessToken = (String) response.get("access_token");
                    if (accessToken == null) {
                        throw new RuntimeException("响应中未找到access_token");
                    }
                    logger.info("成功获取访问令牌");
                    return accessToken;
                });
    }
    
    /**
     * 缓存令牌
     */
    private void cacheToken(String accessToken) {
        this.cachedAccessToken = accessToken;
        // 设置过期时间为55分钟后（令牌通常1小时过期，提前5分钟刷新）
        this.tokenExpiryTime = LocalDateTime.now().plusMinutes(55);
        logger.debug("访问令牌已缓存，过期时间: {}", tokenExpiryTime);
    }
    
    /**
     * 检查缓存的令牌是否有效
     */
    private boolean isTokenValid() {
        return cachedAccessToken != null && 
               tokenExpiryTime != null && 
               LocalDateTime.now().isBefore(tokenExpiryTime);
    }
    
    /**
     * 清除缓存的令牌
     */
    public void clearCachedToken() {
        this.cachedAccessToken = null;
        this.tokenExpiryTime = null;
        logger.info("已清除缓存的访问令牌");
    }
}
