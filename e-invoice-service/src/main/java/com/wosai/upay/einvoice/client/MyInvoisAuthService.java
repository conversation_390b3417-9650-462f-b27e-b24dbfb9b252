package com.wosai.upay.einvoice.client;

import com.wosai.upay.einvoice.service.TokenCacheService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.Base64;
import java.util.Map;

/**
 * MyInvois OAuth2认证服务
 * 负责获取和管理访问令牌
 */
@Service
public class MyInvoisAuthService {

    private static final Logger logger = LoggerFactory.getLogger(MyInvoisAuthService.class);

    private final WebClient webClient;

    @Autowired
    private TokenCacheService tokenCacheService;

    @Value("${myinvois.auth.base-url:https://myinvois.hasil.gov.my}")
    private String authBaseUrl;

    @Value("${myinvois.auth.client-id}")
    private String clientId;

    @Value("${myinvois.auth.client-secret}")
    private String clientSecret;

    @Value("${myinvois.auth.scope:InvoicingAPI}")
    private String scope;

    @Value("${myinvois.auth.timeout:30}")
    private int timeoutSeconds;

    public MyInvoisAuthService(WebClient.Builder webClientBuilder) {
        this.webClient = webClientBuilder.build();
    }

    /**
     * 获取访问令牌
     * 先从 Redis 缓存中获取，如果不存在或已过期则请求新令牌
     */
    public Mono<String> getAccessToken() {
        // 先从 Redis 缓存中获取
        String cachedToken = tokenCacheService.getCachedAccessToken(clientId);
        if (cachedToken != null) {
            logger.debug("使用Redis缓存的访问令牌，剩余时间: {}s",
                    tokenCacheService.getTokenRemainingTime(clientId));
            return Mono.just(cachedToken);
        }

        logger.info("获取新的访问令牌");
        return requestNewAccessToken()
                .doOnSuccess(tokenResponse -> cacheTokenToRedis(tokenResponse))
                .map(response -> (String) response.get("access_token"))
                .doOnError(error -> logger.error("获取访问令牌失败", error));
    }

    /**
     * 请求新的访问令牌
     */
    private Mono<Map<String, Object>> requestNewAccessToken() {
        String url = authBaseUrl + "/connect/token";

        // 准备请求体
        MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
        formData.add("grant_type", "client_credentials");
        formData.add("client_id", clientId);
        formData.add("client_secret", clientSecret);
        formData.add("scope", scope);

        // 准备Basic认证头
        String credentials = clientId + ":" + clientSecret;
        String encodedCredentials = Base64.getEncoder().encodeToString(credentials.getBytes());

        return webClient.post()
                .uri(url)
                .header(HttpHeaders.AUTHORIZATION, "Basic " + encodedCredentials)
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE)
                .header(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
                .body(BodyInserters.fromFormData(formData))
                .retrieve()
                .onStatus(status -> status.is4xxClientError() || status.is5xxServerError(),
                        response -> response.bodyToMono(String.class)
                                .flatMap(body -> {
                                    logger.error("OAuth2认证失败: {} - {}", response.statusCode(), body);
                                    return Mono.error(new RuntimeException("OAuth2认证失败: " + body));
                                }))
                .bodyToMono(Map.class)
                .map(map -> (Map<String, Object>) map)
                .timeout(Duration.ofSeconds(timeoutSeconds))
                .map(response -> {
                    String accessToken = (String) response.get("access_token");
                    if (accessToken == null) {
                        throw new RuntimeException("响应中未找到access_token");
                    }
                    logger.info("成功获取访问令牌");
                    return response;
                });
    }

    /**
     * 将令牌缓存到Redis
     */
    private void cacheTokenToRedis(Map<String, Object> tokenResponse) {
        try {
            String accessToken = (String) tokenResponse.get("access_token");
            Object expiresInObj = tokenResponse.get("expires_in");

            // 处理expires_in可能是整数或字符串的情况
            long expiresInSeconds = 3600; // 默认1小时
            if (expiresInObj instanceof Integer) {
                expiresInSeconds = ((Integer) expiresInObj).longValue();
            } else if (expiresInObj instanceof Long) {
                expiresInSeconds = (Long) expiresInObj;
            } else if (expiresInObj instanceof String) {
                try {
                    expiresInSeconds = Long.parseLong((String) expiresInObj);
                } catch (NumberFormatException e) {
                    logger.warn("无法解析expires_in值: {}，使用默认值3600秒", expiresInObj);
                }
            }

            tokenCacheService.cacheAccessToken(clientId, accessToken, expiresInSeconds);

        } catch (Exception e) {
            logger.error("缓存令牌到Redis失败", e);
        }
    }

    /**
     * 清除缓存的令牌
     */
    public void clearCachedToken() {
        tokenCacheService.clearCachedToken(clientId);
        logger.info("已清除Redis中缓存的访问令牌");
    }

    /**
     * 获取令牌剩余有效时间
     */
    public long getTokenRemainingTime() {
        return tokenCacheService.getTokenRemainingTime(clientId);
    }

    /**
     * 检查令牌是否有效
     */
    public boolean isTokenValid() {
        return tokenCacheService.isTokenValid(clientId);
    }
}
