package com.wosai.upay.einvoice.controller;

import com.wosai.upay.einvoice.dto.ApiResponse;
import com.wosai.upay.einvoice.dto.InvoiceRequest;
import com.wosai.upay.einvoice.dto.InvoiceResponse;
import com.wosai.upay.einvoice.service.InvoiceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 电子发票控制器
 */
@RestController
@RequestMapping("/api/v1/invoices")
@CrossOrigin(origins = "*")
public class InvoiceController {
    
    @Autowired
    private InvoiceService invoiceService;
    
    /**
     * 创建电子发票
     */
    @PostMapping
    public ResponseEntity<ApiResponse<InvoiceResponse>> createInvoice(@Valid @RequestBody InvoiceRequest request) {
        try {
            InvoiceResponse response = invoiceService.createInvoice(request);
            return ResponseEntity.ok(ApiResponse.success("发票创建成功", response));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(400, "发票创建失败: " + e.getMessage()));
        }
    }
    
    /**
     * 根据ID查询发票
     */
    @GetMapping("/{invoiceId}")
    public ResponseEntity<ApiResponse<InvoiceResponse>> getInvoice(@PathVariable String invoiceId) {
        try {
            InvoiceResponse response = invoiceService.getInvoiceById(invoiceId);
            if (response != null) {
                return ResponseEntity.ok(ApiResponse.success(response));
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(400, "查询发票失败: " + e.getMessage()));
        }
    }
    
    /**
     * 查询发票列表
     */
    @GetMapping
    public ResponseEntity<ApiResponse<List<InvoiceResponse>>> getInvoices(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String status) {
        try {
            List<InvoiceResponse> responses = invoiceService.getInvoices(page, size, status);
            return ResponseEntity.ok(ApiResponse.success(responses));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(400, "查询发票列表失败: " + e.getMessage()));
        }
    }
    
    /**
     * 取消发票
     */
    @DeleteMapping("/{invoiceId}")
    public ResponseEntity<ApiResponse<String>> cancelInvoice(@PathVariable String invoiceId) {
        try {
            invoiceService.cancelInvoice(invoiceId);
            return ResponseEntity.ok(ApiResponse.success("发票取消成功", "发票已取消"));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(400, "发票取消失败: " + e.getMessage()));
        }
    }
    
    /**
     * 健康检查
     */
    @GetMapping("/health")
    public ResponseEntity<ApiResponse<String>> health() {
        return ResponseEntity.ok(ApiResponse.success("服务正常", "E-Invoice Service is running"));
    }
}
