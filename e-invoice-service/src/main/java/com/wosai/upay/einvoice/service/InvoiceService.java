package com.wosai.upay.einvoice.service;

import com.wosai.upay.einvoice.dto.InvoiceRequest;
import com.wosai.upay.einvoice.dto.InvoiceResponse;

import java.util.List;

/**
 * 发票服务接口
 */
public interface InvoiceService {
    
    /**
     * 创建发票
     */
    InvoiceResponse createInvoice(InvoiceRequest request);
    
    /**
     * 根据ID获取发票
     */
    InvoiceResponse getInvoiceById(String invoiceId);
    
    /**
     * 获取发票列表
     */
    List<InvoiceResponse> getInvoices(int page, int size, String status);
    
    /**
     * 取消发票
     */
    void cancelInvoice(String invoiceId);
}
