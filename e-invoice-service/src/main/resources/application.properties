# =================================================================
# E-Invoice Service - ?????
# =================================================================

# ??????
spring.application.name=e-invoice-service
server.port=8080
server.servlet.context-path=/

# ??????? (default, beta, prod)
spring.profiles.active=${SPRING_PROFILES_ACTIVE:default}

# Jackson JSON??
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8
spring.jackson.serialization.write-dates-as-timestamps=false

# ????
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n

# MyInvois API ????
myinvois.api.timeout=${MYINVOIS_API_TIMEOUT:30}
myinvois.auth.scope=${MYINVOIS_SCOPE:InvoicingAPI}
myinvois.auth.timeout=${MYINVOIS_AUTH_TIMEOUT:30}

# ??????
management.endpoints.web.exposure.include=health,info,metrics,env,configprops
management.endpoint.health.show-details=always
management.endpoint.env.show-values=when-authorized

# Redis ????
spring.redis.timeout=3000ms
spring.redis.jedis.pool.max-wait=${REDIS_MAX_WAIT:1000ms}
