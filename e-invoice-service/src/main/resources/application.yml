server:
  port: 8080
  servlet:
    context-path: /

spring:
  application:
    name: e-invoice-service
  
  profiles:
    active: dev
  
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: password
  
  h2:
    console:
      enabled: true
      path: /h2-console
  
  jpa:
    database-platform: org.hibernate.dialect.H2Dialect
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        format_sql: true
  
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false

logging:
  level:
    com.wosai.upay.einvoice: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always

---
spring:
  profiles: dev
  
logging:
  level:
    root: INFO
    com.wosai.upay.einvoice: DEBUG

---
spring:
  profiles: prod
  
  datasource:
    url: ********************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: ${DB_USERNAME:einvoice}
    password: ${DB_PASSWORD:password}
  
  jpa:
    database-platform: org.hibernate.dialect.MySQL8Dialect
    hibernate:
      ddl-auto: validate
    show-sql: false

logging:
  level:
    root: WARN
    com.wosai.upay.einvoice: INFO
