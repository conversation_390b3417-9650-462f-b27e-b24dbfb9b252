server:
  port: 8080
  servlet:
    context-path: /

spring:
  application:
    name: e-invoice-service

  profiles:
    active: dev

  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: password

  h2:
    console:
      enabled: true
      path: /h2-console

  jpa:
    database-platform: org.hibernate.dialect.H2Dialect
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        format_sql: true

  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false

logging:
  level:
    com.wosai.upay.einvoice: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# MyInvois API配置
myinvois:
  api:
    base-url: ${MYINVOIS_API_BASE_URL:https://myinvois.hasil.gov.my}
    timeout: ${MYINVOIS_API_TIMEOUT:30}
  auth:
    base-url: ${MYINVOIS_AUTH_BASE_URL:https://myinvois.hasil.gov.my}
    client-id: ${MYINVOIS_CLIENT_ID:your-client-id}
    client-secret: ${MYINVOIS_CLIENT_SECRET:your-client-secret}
    scope: ${MYINVOIS_SCOPE:InvoicingAPI}
    timeout: ${MYINVOIS_AUTH_TIMEOUT:30}

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always

---
spring:
  profiles: dev

# 开发环境使用沙盒环境
myinvois:
  api:
    base-url: https://myinvois-sandbox.hasil.gov.my
  auth:
    base-url: https://myinvois-sandbox.hasil.gov.my
    client-id: ${MYINVOIS_SANDBOX_CLIENT_ID:sandbox-client-id}
    client-secret: ${MYINVOIS_SANDBOX_CLIENT_SECRET:sandbox-client-secret}

logging:
  level:
    root: INFO
    com.wosai.upay.einvoice: DEBUG
    org.springframework.web.reactive.function.client: DEBUG

---
spring:
  profiles: prod

  datasource:
    url: ********************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: ${DB_USERNAME:einvoice}
    password: ${DB_PASSWORD:password}

  jpa:
    database-platform: org.hibernate.dialect.MySQL8Dialect
    hibernate:
      ddl-auto: validate
    show-sql: false

# 生产环境使用正式环境
myinvois:
  api:
    base-url: https://myinvois.hasil.gov.my
  auth:
    base-url: https://myinvois.hasil.gov.my
    client-id: ${MYINVOIS_PROD_CLIENT_ID}
    client-secret: ${MYINVOIS_PROD_CLIENT_SECRET}

logging:
  level:
    root: WARN
    com.wosai.upay.einvoice: INFO
