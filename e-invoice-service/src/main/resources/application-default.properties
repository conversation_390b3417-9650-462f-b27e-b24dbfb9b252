# =================================================================
# E-Invoice Service - Default???? (????)
# =================================================================

# ????? - H2?????
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=password

# H2?????
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# JPA??
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# MyInvois?? - ????
myinvois.api.base-url=${MYINVOIS_API_BASE_URL:https://myinvois-sandbox.hasil.gov.my}
myinvois.auth.base-url=${MYINVOIS_AUTH_BASE_URL:https://myinvois-sandbox.hasil.gov.my}
myinvois.auth.client-id=${MYINVOIS_SANDBOX_CLIENT_ID:your-sandbox-client-id}
myinvois.auth.client-secret=${MYINVOIS_SANDBOX_CLIENT_SECRET:your-sandbox-client-secret}

# Redis?? - ??????
spring.redis.host=${REDIS_HOST:localhost}
spring.redis.port=${REDIS_PORT:6379}
spring.redis.database=${REDIS_DATABASE:0}
spring.redis.password=${REDIS_PASSWORD:}
spring.redis.jedis.pool.max-active=8
spring.redis.jedis.pool.max-idle=8
spring.redis.jedis.pool.min-idle=0

# ???? - ????
logging.level.root=INFO
logging.level.com.wosai.upay.einvoice=DEBUG
logging.level.org.springframework.web.reactive.function.client=DEBUG
logging.level.org.springframework.data.redis=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

# ??????
debug=false
