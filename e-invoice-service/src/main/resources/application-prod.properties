# =================================================================
# E-Invoice Service - Prod???? (????)
# =================================================================

# ????? - MySQL????
spring.datasource.url=jdbc:mysql://${DB_HOST}:${DB_PORT:3306}/${DB_NAME:einvoice}?useSSL=true&serverTimezone=UTC&characterEncoding=utf8
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.username=${DB_USERNAME}
spring.datasource.password=${DB_PASSWORD}

# ???????? - ??????
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=10
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000
spring.datasource.hikari.leak-detection-threshold=60000

# JPA?? - ??????
spring.jpa.database-platform=org.hibernate.dialect.MySQL8Dialect
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.properties.hibernate.jdbc.batch_size=20
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true

# MyInvois?? - ????
myinvois.api.base-url=${MYINVOIS_API_BASE_URL:https://myinvois.hasil.gov.my}
myinvois.auth.base-url=${MYINVOIS_AUTH_BASE_URL:https://myinvois.hasil.gov.my}
myinvois.auth.client-id=${MYINVOIS_PROD_CLIENT_ID}
myinvois.auth.client-secret=${MYINVOIS_PROD_CLIENT_SECRET}

# Redis?? - ???Redis (?????)
spring.redis.host=${REDIS_HOST:r-8vbkddg0ez3eak2rzq.redis.zhangbei.rds.aliyuncs.com}
spring.redis.port=${REDIS_PORT:6379}
spring.redis.database=${REDIS_DATABASE:110}
spring.redis.password=${REDIS_PASSWORD:roFXzHwXPY3RnI%5}
spring.redis.jedis.pool.max-active=20
spring.redis.jedis.pool.max-idle=10
spring.redis.jedis.pool.min-idle=5

# ???? - ????
logging.level.root=WARN
logging.level.com.wosai.upay.einvoice=INFO
logging.level.org.springframework.web.reactive.function.client=ERROR
logging.level.org.springframework.data.redis=WARN

# ??????
debug=false

# ????
management.endpoint.env.show-values=never
management.endpoints.web.exposure.include=health,info,metrics
