# =================================================================
# E-Invoice Service - Beta???? (??????)
# =================================================================

# ????? - MySQL
spring.datasource.url=jdbc:mysql://${DB_HOST:localhost}:${DB_PORT:3306}/${DB_NAME:einvoice_beta}?useSSL=false&serverTimezone=UTC&characterEncoding=utf8
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.username=${DB_USERNAME:einvoice}
spring.datasource.password=${DB_PASSWORD:password}

# ????????
spring.datasource.hikari.maximum-pool-size=10
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000

# JPA??
spring.jpa.database-platform=org.hibernate.dialect.MySQL8Dialect
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false

# MyInvois?? - ???? (Beta???????????)
myinvois.api.base-url=${MYINVOIS_API_BASE_URL:https://myinvois-sandbox.hasil.gov.my}
myinvois.auth.base-url=${MYINVOIS_AUTH_BASE_URL:https://myinvois-sandbox.hasil.gov.my}
myinvois.auth.client-id=${MYINVOIS_BETA_CLIENT_ID}
myinvois.auth.client-secret=${MYINVOIS_BETA_CLIENT_SECRET}

# Redis?? - ???Redis (Beta???)
spring.redis.host=${REDIS_HOST:r-8vbkddg0ez3eak2rzq.redis.zhangbei.rds.aliyuncs.com}
spring.redis.port=${REDIS_PORT:6379}
spring.redis.database=${REDIS_DATABASE:111}
spring.redis.password=${REDIS_PASSWORD:roFXzHwXPY3RnI%5}
spring.redis.jedis.pool.max-active=15
spring.redis.jedis.pool.max-idle=10
spring.redis.jedis.pool.min-idle=5

# ???? - ????
logging.level.root=INFO
logging.level.com.wosai.upay.einvoice=INFO
logging.level.org.springframework.web.reactive.function.client=WARN
logging.level.org.springframework.data.redis=INFO

# ??????
debug=false
