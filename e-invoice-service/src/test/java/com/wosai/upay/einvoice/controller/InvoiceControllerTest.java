package com.wosai.upay.einvoice.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.upay.einvoice.dto.InvoiceItem;
import com.wosai.upay.einvoice.dto.InvoiceRequest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.util.Arrays;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@AutoConfigureWebMvc
class InvoiceControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void testCreateInvoice() throws Exception {
        InvoiceRequest request = createTestInvoiceRequest();

        mockMvc.perform(post("/api/v1/invoices")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("发票创建成功"))
                .andExpect(jsonPath("$.data.invoice_number").exists());
    }

    @Test
    void testHealthCheck() throws Exception {
        mockMvc.perform(get("/api/v1/invoices/health"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").value("E-Invoice Service is running"));
    }

    private InvoiceRequest createTestInvoiceRequest() {
        InvoiceRequest request = new InvoiceRequest();
        request.setBuyerName("测试买方公司");
        request.setBuyerTaxNumber("***************");
        request.setBuyerAddress("测试买方地址");
        request.setBuyerPhone("***********");
        request.setBuyerBankAccount("**********");

        request.setSellerName("测试卖方公司");
        request.setSellerTaxNumber("***************");
        request.setSellerAddress("测试卖方地址");
        request.setSellerPhone("***********");
        request.setSellerBankAccount("**********");

        InvoiceItem item = new InvoiceItem();
        item.setItemName("测试商品");
        item.setSpecification("规格型号");
        item.setUnit("个");
        item.setQuantity(new BigDecimal("2"));
        item.setUnitPrice(new BigDecimal("100.00"));
        item.setAmount(new BigDecimal("200.00"));
        item.setTaxRate(new BigDecimal("0.13"));
        item.setTaxAmount(new BigDecimal("26.00"));

        request.setInvoiceItems(Arrays.asList(item));
        request.setRemarks("测试备注");

        return request;
    }
}
