<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.wosai.upay</groupId>
        <artifactId>e-invoice</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>e-invoice-service</artifactId>
    <packaging>jar</packaging>
    <version>${project.parent.version}</version>
    <name>e-invoice-service</name>

    <properties>
        <java.version>24</java.version>
        <maven.compiler.source>24</maven.compiler.source>
        <maven.compiler.target>24</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>

        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>e-invoice-api</artifactId>
            <version>${project.parent.version}</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

    <build>
        <finalName>e-invoice</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <!--maven install|deploy时，跳过本模块-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-install-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.wosai.middleware</groupId>
                <artifactId>wosai-logging-maven-plugin</artifactId>
                <version>${shouqianba.logging-plugin.version}</version>
                <configuration>
                    <enableCallerData>true</enableCallerData>
                    <patternLayout>%d{yyyy-MM-dd HH:mm:ss.SSS} %contextName [%thread] [%tid] %-5level %logger{36}.%M -
                        %msg%n
                    </patternLayout>
                    <profiles>
                        <profile>
                            <name>prod</name>
                            <level>INFO</level>
                            <references>
                                <ref>FT_CONSOLE_JSON</ref> <!-- 输出到标准输出，格式是JSON -->
                            </references>
                        </profile>
                        <profile>
                            <name>beta</name>
                            <level>INFO</level>
                            <references>
                                <ref>FT_FILE_PATTERN</ref> <!-- 输出到文件，格式是格式化字符串 -->
                                <ref>FT_CONSOLE_PATTERN</ref> <!-- 输出到标准输出，格式是格式化字符串 -->
                            </references>
                        </profile>
                        <profile>
                            <name>default</name>   <!-- 在本地开发调试时，在IDE中设置 active profile为default -->
                            <level>INFO</level>
                            <references>
                                <ref>FT_CONSOLE_PATTERN</ref> <!-- 输出到标准输出，格式是格式化字符串 (1.2.0新增功能) -->
                            </references>
                        </profile>
                    </profiles>
                    <scopes>
                        <scope>
                            <name>org.mybatis.spring</name>
                            <level>WARN</level>
                        </scope>
                        <scope>
                            <name>com.wosai.deposit.dao.mapper</name>
                            <level>WARN</level>
                        </scope>
                        <scope>
                            <name>org.springframework.jdbc.datasource.DataSourceUtils</name>
                            <level>WARN</level>
                        </scope>
                    </scopes>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>generate-logback-spring</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>