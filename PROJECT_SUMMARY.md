# 电子发票服务 (E-Invoice Service)

## 项目概述

这是一个基于Spring Boot的电子发票管理系统，提供完整的电子发票创建、查询、管理功能。

## 技术栈

- **Java 1.8**
- **Spring Boot 2.7.17**
- **Spring Data JPA**
- **H2 Database** (开发环境)
- **Maven** (构建工具)
- **<PERSON>** (JSON处理)
- **Hibernate** (ORM)

## 项目结构

```
e-invoice/
├── e-invoice-api/          # API模块 - 包含DTO和接口定义
│   └── src/main/java/com/wosai/upay/einvoice/dto/
│       ├── InvoiceRequest.java     # 发票创建请求DTO
│       ├── InvoiceResponse.java    # 发票响应DTO
│       ├── InvoiceItem.java        # 发票明细项DTO
│       └── ApiResponse.java        # 统一API响应格式
├── e-invoice-service/      # 服务模块 - 包含业务逻辑和REST API
│   └── src/main/java/com/wosai/upay/einvoice/
│       ├── EInvoiceApplication.java        # Spring Boot主应用类
│       ├── controller/
│       │   └── InvoiceController.java      # REST API控制器
│       ├── service/
│       │   ├── InvoiceService.java         # 服务接口
│       │   └── impl/InvoiceServiceImpl.java # 服务实现
│       ├── entity/
│       │   └── Invoice.java                # 发票实体类
│       ├── repository/
│       │   └── InvoiceRepository.java      # 数据访问层
│       └── exception/
│           └── GlobalExceptionHandler.java # 全局异常处理
└── pom.xml                 # 根POM配置
```

## 核心功能

### 1. 发票管理
- ✅ 创建电子发票
- ✅ 根据ID查询发票
- ✅ 分页查询发票列表
- ✅ 根据状态筛选发票
- ✅ 取消发票

### 2. 数据验证
- ✅ 请求参数验证
- ✅ 业务规则验证
- ✅ 全局异常处理

### 3. 数据库支持
- ✅ H2内存数据库 (开发环境)
- ✅ MySQL支持 (生产环境配置)
- ✅ JPA自动建表
- ✅ 数据库控制台

## API接口

### 基础URL
```
http://localhost:8080/api/v1/invoices
```

### 接口列表

#### 1. 健康检查
```bash
GET /api/v1/invoices/health
```

#### 2. 创建发票
```bash
POST /api/v1/invoices
Content-Type: application/json

{
  "buyer_name": "测试买方公司",
  "buyer_tax_number": "**********12345",
  "buyer_address": "测试买方地址",
  "buyer_phone": "***********",
  "buyer_bank_account": "**********",
  "seller_name": "测试卖方公司",
  "seller_tax_number": "***************",
  "seller_address": "测试卖方地址",
  "seller_phone": "***********",
  "seller_bank_account": "**********",
  "invoice_items": [
    {
      "item_name": "测试商品",
      "specification": "规格型号",
      "unit": "个",
      "quantity": 2,
      "unit_price": 100.00,
      "amount": 200.00,
      "tax_rate": 0.13,
      "tax_amount": 26.00
    }
  ],
  "remarks": "测试备注"
}
```

#### 3. 查询发票
```bash
GET /api/v1/invoices/{invoiceId}
```

#### 4. 查询发票列表
```bash
GET /api/v1/invoices?page=0&size=10&status=ISSUED
```

#### 5. 取消发票
```bash
DELETE /api/v1/invoices/{invoiceId}
```

## 快速开始

### 1. 环境要求
- Java 1.8+
- Maven 3.6+

### 2. 构建项目
```bash
mvn clean package -DskipTests
```

### 3. 运行应用
```bash
java -jar e-invoice-service/target/e-invoice.jar
```

### 4. 访问应用
- 应用地址: http://localhost:8080
- H2控制台: http://localhost:8080/h2-console
  - JDBC URL: `jdbc:h2:mem:testdb`
  - 用户名: `sa`
  - 密码: `password`

### 5. 测试API
```bash
# 健康检查
curl -X GET http://localhost:8080/api/v1/invoices/health

# 创建发票
curl -X POST http://localhost:8080/api/v1/invoices \
  -H "Content-Type: application/json" \
  -d @test_invoice.json
```

## 配置说明

### 开发环境 (dev)
- 使用H2内存数据库
- 启用SQL日志
- 启用H2控制台

### 生产环境 (prod)
- 使用MySQL数据库
- 关闭SQL日志
- 优化日志级别

## 数据库表结构

### invoices 表
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 主键ID |
| invoice_id | VARCHAR | 发票唯一标识 |
| invoice_number | VARCHAR | 发票号码 |
| invoice_code | VARCHAR | 发票代码 |
| buyer_name | VARCHAR | 买方名称 |
| buyer_tax_number | VARCHAR | 买方税号 |
| seller_name | VARCHAR | 卖方名称 |
| seller_tax_number | VARCHAR | 卖方税号 |
| total_amount | DECIMAL | 合计金额 |
| tax_amount | DECIMAL | 税额 |
| amount_including_tax | DECIMAL | 价税合计 |
| status | VARCHAR | 发票状态 |
| issue_date | TIMESTAMP | 开票日期 |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

## 特性亮点

1. **模块化设计**: API和Service分离，便于维护和扩展
2. **统一响应格式**: 所有API返回统一的JSON格式
3. **参数验证**: 使用Bean Validation进行请求参数验证
4. **全局异常处理**: 统一处理各种异常情况
5. **数据库支持**: 支持H2和MySQL数据库
6. **配置管理**: 支持多环境配置
7. **日志管理**: 完善的日志配置
8. **健康检查**: 提供应用健康状态检查
9. **测试支持**: 包含单元测试和集成测试

## 扩展建议

1. **安全认证**: 添加JWT或OAuth2认证
2. **文件生成**: 实现PDF和XML发票文件生成
3. **消息队列**: 添加异步处理能力
4. **缓存支持**: 添加Redis缓存
5. **监控告警**: 集成Prometheus和Grafana
6. **API文档**: 集成Swagger/OpenAPI
7. **容器化**: 添加Docker和Kubernetes支持

## 开发团队

- 基于Spring Boot最佳实践开发
- 遵循RESTful API设计规范
- 采用分层架构设计模式
