# 马来西亚电子发票服务 (Malaysia E-Invoice Service)

## 项目概述

这是一个基于Spring Boot的马来西亚电子发票管理系统，完全集成MyInvois API，提供符合马来西亚税务局(LHDNM)要求的电子发票创建、提交、查询和管理功能。

## 技术栈

- **Java 1.8**
- **Spring Boot 2.7.17**
- **Spring WebFlux** (异步非阻塞HTTP客户端)
- **Spring Security** (OAuth2认证)
- **Spring Data JPA**
- **H2 Database** (开发环境)
- **Maven** (构建工具)
- **Jackson** (JSON/XML处理)
- **Hibernate** (ORM)
- **Apache Santuario** (数字签名)
- **Commons Codec** (Base64编码)

## 项目结构

```
e-invoice/
├── e-invoice-api/          # API模块 - 包含DTO和接口定义
│   └── src/main/java/com/wosai/upay/einvoice/dto/
│       ├── InvoiceRequest.java     # 发票创建请求DTO
│       ├── InvoiceResponse.java    # 发票响应DTO
│       ├── InvoiceItem.java        # 发票明细项DTO
│       ├── ApiResponse.java        # 统一API响应格式
│       ├── myinvois/               # MyInvois API相关DTO
│       │   ├── MyInvoisInvoiceRequest.java
│       │   └── MyInvoisInvoiceResponse.java
│       └── ubl/                    # UBL 2.1标准格式
│           ├── UBLInvoice.java
│           └── Party.java
├── e-invoice-service/      # 服务模块 - 包含业务逻辑和REST API
│   └── src/main/java/com/wosai/upay/einvoice/
│       ├── EInvoiceApplication.java        # Spring Boot主应用类
│       ├── controller/
│       │   ├── InvoiceController.java      # 本地发票API控制器
│       │   └── MyInvoisController.java     # MyInvois集成API控制器
│       ├── service/
│       │   ├── InvoiceService.java         # 服务接口
│       │   ├── impl/InvoiceServiceImpl.java # 服务实现
│       │   └── UBLDocumentService.java     # UBL文档处理服务
│       ├── client/
│       │   ├── MyInvoisApiClient.java      # MyInvois API客户端
│       │   └── MyInvoisAuthService.java    # OAuth2认证服务
│       ├── config/
│       │   └── WebClientConfig.java        # WebClient配置
│       ├── entity/
│       │   └── Invoice.java                # 发票实体类
│       ├── repository/
│       │   └── InvoiceRepository.java      # 数据访问层
│       └── exception/
│           └── GlobalExceptionHandler.java # 全局异常处理
└── pom.xml                 # 根POM配置
```

## 核心功能

### 1. MyInvois集成
- ✅ OAuth2客户端凭证流认证
- ✅ UBL 2.1标准发票格式支持
- ✅ 自动提交发票到MyInvois系统
- ✅ 数字签名支持
- ✅ 沙盒和生产环境支持

### 2. 发票管理
- ✅ 创建电子发票
- ✅ 根据ID查询发票
- ✅ 分页查询发票列表
- ✅ 根据状态筛选发票
- ✅ 取消发票
- ✅ MyInvois文档状态同步

### 3. UBL 2.1标准支持
- ✅ 符合马来西亚税务局规范
- ✅ JSON和XML格式输出
- ✅ 数据验证和校验
- ✅ Base64编码和SHA256哈希

### 4. 数据验证
- ✅ 请求参数验证
- ✅ 业务规则验证
- ✅ 全局异常处理

### 5. 数据库支持
- ✅ H2内存数据库 (开发环境)
- ✅ MySQL支持 (生产环境配置)
- ✅ JPA自动建表
- ✅ 数据库控制台

## API接口

### 基础URL
```
http://localhost:8080/api/v1/invoices    # 本地发票API
http://localhost:8080/api/v1/myinvois    # MyInvois集成API
```

### 本地发票API

#### 1. 健康检查
```bash
GET /api/v1/invoices/health
```

#### 2. 创建发票
```bash
POST /api/v1/invoices
Content-Type: application/json

{
  "buyer_name": "测试买方公司",
  "buyer_tax_number": "**********12345",
  "buyer_address": "测试买方地址",
  "buyer_phone": "***********",
  "buyer_bank_account": "**********",
  "seller_name": "测试卖方公司",
  "seller_tax_number": "***************",
  "seller_address": "测试卖方地址",
  "seller_phone": "***********",
  "seller_bank_account": "**********",
  "invoice_items": [
    {
      "item_name": "测试商品",
      "specification": "规格型号",
      "unit": "个",
      "quantity": 2,
      "unit_price": 100.00,
      "amount": 200.00,
      "tax_rate": 0.13,
      "tax_amount": 26.00
    }
  ],
  "remarks": "测试备注"
}
```

#### 3. 查询发票
```bash
GET /api/v1/invoices/{invoiceId}
```

#### 4. 查询发票列表
```bash
GET /api/v1/invoices?page=0&size=10&status=ISSUED
```

#### 5. 取消发票
```bash
DELETE /api/v1/invoices/{invoiceId}
```

### MyInvois集成API

#### 1. 获取MyInvois文档详情
```bash
GET /api/v1/myinvois/documents/{uuid}
```

#### 2. 取消MyInvois文档
```bash
PUT /api/v1/myinvois/documents/{uuid}/cancel?reason=取消原因
```

#### 3. 获取提交状态
```bash
GET /api/v1/myinvois/submissions/{submissionUid}
```

#### 4. MyInvois健康检查
```bash
GET /api/v1/myinvois/health
```

## 快速开始

### 1. 环境要求
- Java 1.8+
- Maven 3.6+

### 2. 构建项目
```bash
mvn clean package -DskipTests
```

### 3. 运行应用
```bash
java -jar e-invoice-service/target/e-invoice.jar
```

### 4. 访问应用
- 应用地址: http://localhost:8080
- H2控制台: http://localhost:8080/h2-console
  - JDBC URL: `jdbc:h2:mem:testdb`
  - 用户名: `sa`
  - 密码: `password`

### 5. 测试API
```bash
# 健康检查
curl -X GET http://localhost:8080/api/v1/invoices/health

# 创建发票
curl -X POST http://localhost:8080/api/v1/invoices \
  -H "Content-Type: application/json" \
  -d @test_invoice.json
```

## 配置说明

### MyInvois配置

在`application.yml`中配置你的MyInvois凭证：

```yaml
myinvois:
  api:
    base-url: https://myinvois-sandbox.hasil.gov.my  # 沙盒环境
  auth:
    client-id: 你的客户端ID
    client-secret: 你的客户端密钥
    scope: InvoicingAPI
```

或者通过环境变量设置：
```bash
export MYINVOIS_CLIENT_ID=你的客户端ID
export MYINVOIS_CLIENT_SECRET=你的客户端密钥
```

### 环境配置

#### 开发环境 (dev)
- 使用H2内存数据库
- 使用MyInvois沙盒环境
- 启用SQL日志
- 启用H2控制台

#### 生产环境 (prod)
- 使用MySQL数据库
- 使用MyInvois正式环境
- 关闭SQL日志
- 优化日志级别

## 数据库表结构

### invoices 表
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 主键ID |
| invoice_id | VARCHAR | 发票唯一标识 |
| invoice_number | VARCHAR | 发票号码 |
| invoice_code | VARCHAR | 发票代码 |
| buyer_name | VARCHAR | 买方名称 |
| buyer_tax_number | VARCHAR | 买方税号 |
| seller_name | VARCHAR | 卖方名称 |
| seller_tax_number | VARCHAR | 卖方税号 |
| total_amount | DECIMAL | 合计金额 |
| tax_amount | DECIMAL | 税额 |
| amount_including_tax | DECIMAL | 价税合计 |
| status | VARCHAR | 发票状态 |
| issue_date | TIMESTAMP | 开票日期 |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

## 特性亮点

1. **完整MyInvois集成**: 完全符合马来西亚税务局规范
2. **UBL 2.1标准**: 支持国际电子商务标准
3. **OAuth2认证**: 安全的API访问控制
4. **异步处理**: 使用WebFlux实现高性能异步调用
5. **模块化设计**: API和Service分离，便于维护和扩展
6. **统一响应格式**: 所有API返回统一的JSON格式
7. **参数验证**: 使用Bean Validation进行请求参数验证
8. **全局异常处理**: 统一处理各种异常情况
9. **数据库支持**: 支持H2和MySQL数据库
10. **多环境支持**: 沙盒和生产环境配置
11. **日志管理**: 完善的日志配置
12. **健康检查**: 提供应用健康状态检查
13. **测试支持**: 包含单元测试和集成测试

## 重要注意事项

### MyInvois集成须知

1. **注册要求**: 需要在MyInvois系统注册并获取API凭证
2. **沙盒测试**: 建议先在沙盒环境充分测试
3. **数字签名**: 生产环境需要配置数字证书
4. **税务规范**: 确保发票内容符合马来西亚税务法规
5. **数据备份**: 建议定期备份发票数据

### 性能优化

1. **连接池**: 配置合适的HTTP连接池大小
2. **超时设置**: 根据网络环境调整超时参数
3. **缓存策略**: 合理使用访问令牌缓存
4. **批量处理**: 对于大量发票，考虑批量提交

## 扩展建议

1. **数字签名**: 完整实现XML数字签名功能
2. **文件生成**: 实现PDF发票文件生成
3. **消息队列**: 添加异步处理能力
4. **缓存支持**: 添加Redis缓存
5. **监控告警**: 集成Prometheus和Grafana
6. **API文档**: 集成Swagger/OpenAPI
7. **容器化**: 添加Docker和Kubernetes支持
8. **数据同步**: 实现与ERP系统的数据同步

## 开发团队

- 基于Spring Boot最佳实践开发
- 遵循RESTful API设计规范
- 采用分层架构设计模式
- 完全符合MyInvois API规范
